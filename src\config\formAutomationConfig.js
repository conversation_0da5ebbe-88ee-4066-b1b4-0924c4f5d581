module.exports = {
  // Browser configuration
  browser: {
    headless: process.env.NODE_ENV === "production",
    timeout: 30000,
    navigationTimeout: 30000,
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
      "--no-first-run",
      "--no-zygote",
      "--single-process",
      "--disable-gpu",
      "--disable-web-security",
      "--disable-features=VizDisplayCompositor",
    ],
  },

  // Platform-specific configurations
  platforms: {
    "funda.nl": {
      selectors: {
        contactButton: '[data-optimizely="contact-email"]',
        questionField: "#questionInput",
        viewingCheckbox: "#checkbox-viewingRequest",
        emailField: "#emailAddress",
        firstNameField: "#firstName",
        lastNameField: "#lastName",
        phoneField: "#phoneNumber",
        submitButton: 'button[type="submit"]',
      },
      waitTimes: {
        afterClick: 3000,
        afterSubmit: 3000,
        formLoad: 10000,
      },
    },

    "pararius.nl": {
      selectors: {
        applyButton: ".contact-button, .btn-contact",
        nameField: 'input[name="name"], #contact_name',
        emailField: 'input[name="email"], #contact_email',
        phoneField: 'input[name="phone"], #contact_phone',
        messageField: 'textarea[name="message"], #contact_message',
        submitButton: 'button[type="submit"], .btn-submit',
      },
      waitTimes: {
        afterClick: 2000,
        afterSubmit: 3000,
      },
    },

    "kamernet.nl": {
      selectors: {
        applyButton: '.react-button, button:contains("Reageren")',
        nameField: 'input[name="name"], #name',
        emailField: 'input[name="email"], #email',
        phoneField: 'input[name="phone"], #phone',
        messageField: 'textarea[name="message"], #message',
        submitButton: 'button[type="submit"], .submit-button',
      },
      waitTimes: {
        afterClick: 2000,
        afterSubmit: 3000,
      },
      requiresLogin: true,
    },

    "huurwoningen.nl": {
      selectors: {
        applyButton: '.apply-button, button:contains("Solliciteren")',
        nameField: 'input[name="name"], #applicant_name',
        emailField: 'input[name="email"], #applicant_email',
        phoneField: 'input[name="phone"], #applicant_phone',
        messageField: 'textarea[name="message"], #applicant_message',
        submitButton: 'button[type="submit"], .submit-application',
        incomeField: 'input[name="income"], #income',
        ageField: 'input[name="age"], #age',
        occupationField: 'input[name="occupation"], #occupation',
      },
      waitTimes: {
        afterClick: 2000,
        afterSubmit: 3000,
      },
    },
  },

  // Default message templates
  messageTemplates: {
    dutch: `Beste verhuurder,

Ik ben zeer geïnteresseerd in deze woning. Ik ben {age} jaar oud, werk als {occupation} en heb een maandinkomen van €{monthlyIncome}.

{additionalInfo}

Ik hoor graag van u.

Met vriendelijke groet,
{firstName} {lastName}`,

    english: `Dear landlord,

I am very interested in this property. I am {age} years old, work as {occupation} and have a monthly income of €{monthlyIncome}.

{additionalInfo}

I look forward to hearing from you.

Best regards,
{firstName} {lastName}`,
  },

  // Rate limiting
  rateLimiting: {
    maxApplicationsPerHour: 10,
    delayBetweenApplications: 30000, // 30 seconds
    maxRetries: 3,
  },

  // Error handling
  errorHandling: {
    captchaRetries: 1,
    networkTimeoutRetries: 2,
    screenshotOnError: true,
  },

  // Screenshot settings
  screenshots: {
    enabled: true,
    saveOnSuccess: true,
    saveOnError: true,
    saveSteps: true,
    directory: "screenshots",
    format: "png",
    fullPage: true,
  },
};
