/**
 * Real-time monitoring script for auto application system
 * 
 * This script continuously monitors the auto application system
 * and provides real-time status updates
 */

const axios = require('axios');

const API_BASE_URL = process.env.API_URL || 'http://localhost:3000';

// Mock user credentials - replace with actual test user
const TEST_USER = {
  email: process.env.TEST_USER_EMAIL || '<EMAIL>',
  password: process.env.TEST_USER_PASSWORD || 'Testpassword123'
};

class AutoApplicationMonitor {
  constructor() {
    this.authToken = null;
    this.userId = null;
    this.isRunning = false;
    this.intervalId = null;
  }

  async login() {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/auth/login`, {
        email: TEST_USER.email,
        password: TEST_USER.password
      });

      if (response.data.success) {
        this.authToken = response.data.token;
        this.userId = response.data.user.id;
        return true;
      }
      return false;
    } catch (error) {
      console.error('Login failed:', error.message);
      return false;
    }
  }

  getHeaders() {
    return {
      'Authorization': `Bearer ${this.authToken}`,
      'Content-Type': 'application/json'
    };
  }

  async getSystemStatus() {
    try {
      const [settingsRes, statsRes, queueRes] = await Promise.allSettled([
        axios.get(`${API_BASE_URL}/api/auto-application/settings/${this.userId}`, {
          headers: this.getHeaders()
        }),
        axios.get(`${API_BASE_URL}/api/auto-application/stats/${this.userId}`, {
          headers: this.getHeaders()
        }),
        axios.get(`${API_BASE_URL}/api/auto-application/queue/${this.userId}`, {
          headers: this.getHeaders()
        })
      ]);

      const settings = settingsRes.status === 'fulfilled' ? settingsRes.value.data.data : null;
      const stats = statsRes.status === 'fulfilled' ? statsRes.value.data.data : null;
      const queue = queueRes.status === 'fulfilled' ? queueRes.value.data.data : [];

      return { settings, stats, queue };
    } catch (error) {
      console.error('Error getting system status:', error.message);
      return { settings: null, stats: null, queue: [] };
    }
  }

  displayStatus(status) {
    // Clear console
    console.clear();
    
    const now = new Date().toLocaleString();
    console.log('🤖 Auto Application System Monitor');
    console.log('==================================');
    console.log(`📅 Last Update: ${now}\n`);

    // System Status
    console.log('🔧 System Status:');
    if (status.settings) {
      console.log(`   ✅ Auto Application: ${status.settings.enabled ? 'ENABLED' : 'DISABLED'}`);
      console.log(`   📊 Max Apps/Day: ${status.settings.maxApplicationsPerDay}`);
      console.log(`   🎯 Min Quality Score: ${status.settings.minQualityScore}`);
      console.log(`   📍 Locations: ${status.settings.preferredLocations?.join(', ') || 'None'}`);
    } else {
      console.log('   ❌ Settings: UNAVAILABLE');
    }

    console.log('\n📈 Statistics:');
    if (status.stats) {
      console.log(`   📋 Total Applications: ${status.stats.totalApplications || 0}`);
      console.log(`   ✅ Success Rate: ${status.stats.successRate || 0}%`);
      console.log(`   📅 This Week: ${status.stats.applicationsThisWeek || 0}`);
      console.log(`   📅 Today: ${status.stats.applicationsToday || 0}`);
      console.log(`   ⚡ Avg Response Time: ${status.stats.averageResponseTime || 0}ms`);
    } else {
      console.log('   ❌ Statistics: UNAVAILABLE');
    }

    console.log('\n📋 Queue Status:');
    if (status.queue && status.queue.length > 0) {
      console.log(`   📊 Queue Length: ${status.queue.length}`);
      
      // Count by status
      const statusCounts = status.queue.reduce((acc, item) => {
        acc[item.status] = (acc[item.status] || 0) + 1;
        return acc;
      }, {});

      Object.entries(statusCounts).forEach(([status, count]) => {
        const emoji = {
          'pending': '⏳',
          'processing': '⚡',
          'completed': '✅',
          'failed': '❌',
          'cancelled': '🚫'
        }[status] || '❓';
        
        console.log(`   ${emoji} ${status}: ${count}`);
      });

      // Show next few items
      console.log('\n   📋 Next Items:');
      status.queue.slice(0, 3).forEach((item, index) => {
        const statusEmoji = {
          'pending': '⏳',
          'processing': '⚡',
          'completed': '✅',
          'failed': '❌',
          'cancelled': '🚫'
        }[item.status] || '❓';
        
        console.log(`   ${index + 1}. ${statusEmoji} ${item.listingTitle} (Priority: ${item.priority})`);
      });
    } else {
      console.log('   📭 Queue: EMPTY');
    }

    console.log('\n💡 Commands:');
    console.log('   Ctrl+C: Stop monitoring');
    console.log('   Check logs for detailed information');
  }

  async start(intervalSeconds = 10) {
    console.log('🚀 Starting Auto Application Monitor...');
    
    const loginSuccess = await this.login();
    if (!loginSuccess) {
      console.error('❌ Failed to login. Please check credentials.');
      return;
    }

    console.log('✅ Login successful. Starting monitoring...\n');
    
    this.isRunning = true;
    
    // Initial status check
    const status = await this.getSystemStatus();
    this.displayStatus(status);

    // Set up interval for updates
    this.intervalId = setInterval(async () => {
      if (this.isRunning) {
        const status = await this.getSystemStatus();
        this.displayStatus(status);
      }
    }, intervalSeconds * 1000);

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      this.stop();
    });
  }

  stop() {
    console.log('\n🛑 Stopping monitor...');
    this.isRunning = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    process.exit(0);
  }
}

// Run monitor if this file is executed directly
if (require.main === module) {
  const monitor = new AutoApplicationMonitor();
  
  // Check command line arguments for interval
  const args = process.argv.slice(2);
  const intervalArg = args.find(arg => arg.startsWith('--interval='));
  const interval = intervalArg ? parseInt(intervalArg.split('=')[1]) : 10;
  
  monitor.start(interval).catch(console.error);
}

module.exports = AutoApplicationMonitor;