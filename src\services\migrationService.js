/**
 * Migration Service
 * 
 * This service handles the migration of existing Listing records to the unified schema.
 * It provides functionality for batch processing, validation, and rollback.
 */

const mongoose = require('mongoose');
const { createMinimalProperty } = require('../schemas/unifiedPropertySchema');
const { convertFromLegacyListing } = require('./frontendCompatibilityLayer');
const { SchemaTransformer } = require('./schemaTransformer');
const { ValidationEngine } = require('./validationEngine');

class MigrationService {
  /**
   * Create a new MigrationService instance
   * @param {Object} sourceModel - The source model (e.g., Listing)
   * @param {Object} targetModel - The target model (e.g., EnhancedProperty)
   * @param {Object} options - Configuration options
   */
  constructor(sourceModel, targetModel, options = {}) {
    this.sourceModel = sourceModel;
    this.targetModel = targetModel;
    this.options = {
      batchSize: options.batchSize || 100,
      preserveOriginal: options.preserveOriginal !== false,
      validationLevel: options.validationLevel || 'standard', // 'none', 'standard', 'strict'
      logProgress: options.logProgress !== false,
      systemUserId: options.systemUserId || mongoose.Types.ObjectId('000000000000000000000000'),
      transformationVersion: options.transformationVersion || '1.0.0-migration',
      ...options
    };
    
    this.transformer = options.transformer || null;
    this.validator = options.validator || null;
    
    // Migration statistics
    this.stats = {
      total: 0,
      processed: 0,
      successful: 0,
      skipped: 0,
      failed: 0,
      validationErrors: 0,
      transformationErrors: 0,
      startTime: null,
      endTime: null,
      duration: null
    };
    
    // Store migration history for rollback
    this.migrationHistory = [];
  }
  
  /**
   * Migrate all existing data from source model to target model
   * @param {Object} query - Optional query to filter source records
   * @returns {Object} Migration statistics
   */
  async migrateExistingData(query = {}) {
    try {
      this.stats.startTime = new Date();
      
      // Count total records to migrate
      this.stats.total = await this.sourceModel.countDocuments(query);
      
      if (this.options.logProgress) {
        console.log(`Starting migration of ${this.stats.total} records from ${this.sourceModel.modelName} to ${this.targetModel.modelName}`);
      }
      
      // Process records in batches
      let skip = 0;
      let hasMore = true;
      
      while (hasMore) {
        const batch = await this.sourceModel.find(query)
          .skip(skip)
          .limit(this.options.batchSize)
          .lean();
        
        if (batch.length === 0) {
          hasMore = false;
          break;
        }
        
        await this.migrateBatch(batch);
        
        skip += this.options.batchSize;
        
        if (this.options.logProgress) {
          console.log(`Processed ${Math.min(skip, this.stats.total)} of ${this.stats.total} records (${Math.round(Math.min(skip, this.stats.total) / this.stats.total * 100)}%)`);
        }
      }
      
      this.stats.endTime = new Date();
      this.stats.duration = this.stats.endTime - this.stats.startTime;
      
      if (this.options.logProgress) {
        console.log('Migration completed');
        console.log(`- Total records: ${this.stats.total}`);
        console.log(`- Successfully migrated: ${this.stats.successful}`);
        console.log(`- Skipped (already migrated): ${this.stats.skipped}`);
        console.log(`- Failed: ${this.stats.failed}`);
        console.log(`- Validation errors: ${this.stats.validationErrors}`);
        console.log(`- Transformation errors: ${this.stats.transformationErrors}`);
        console.log(`- Duration: ${Math.round(this.stats.duration / 1000)} seconds`);
      }
      
      return this.stats;
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }
  
  /**
   * Migrate a batch of records
   * @param {Array} records - Array of source records to migrate
   * @returns {Array} Array of migration results
   */
  async migrateBatch(records) {
    const results = [];
    
    for (const record of records) {
      try {
        this.stats.processed++;
        
        // Check if already migrated
        const existingRecord = await this.targetModel.findOne({
          originalListingId: record._id
        });
        
        if (existingRecord) {
          this.stats.skipped++;
          results.push({
            sourceId: record._id,
            targetId: existingRecord._id,
            status: 'skipped',
            reason: 'already_migrated'
          });
          continue;
        }
        
        // Transform the record
        const startTime = Date.now();
        const transformedData = await this.transformRecord(record);
        const processingTime = Date.now() - startTime;
        
        // Validate the transformed data
        let validationResult = { isValid: true, errors: [] };
        if (this.options.validationLevel !== 'none') {
          validationResult = await this.validateMigration(record, transformedData);
          
          if (!validationResult.isValid && this.options.validationLevel === 'strict') {
            this.stats.failed++;
            this.stats.validationErrors++;
            results.push({
              sourceId: record._id,
              status: 'failed',
              reason: 'validation_error',
              errors: validationResult.errors
            });
            continue;
          }
        }
        
        // Create the target record
        const targetRecord = await this.createTargetRecord(record, transformedData, processingTime, validationResult);
        
        // Save the target record
        await targetRecord.save();
        
        // Store migration history for rollback
        this.migrationHistory.push({
          sourceId: record._id,
          targetId: targetRecord._id,
          timestamp: new Date()
        });
        
        this.stats.successful++;
        results.push({
          sourceId: record._id,
          targetId: targetRecord._id,
          status: 'success'
        });
      } catch (error) {
        this.stats.failed++;
        
        if (error.name === 'ValidationError') {
          this.stats.validationErrors++;
        } else {
          this.stats.transformationErrors++;
        }
        
        results.push({
          sourceId: record._id,
          status: 'failed',
          reason: error.name,
          error: error.message
        });
        
        if (this.options.logProgress) {
          console.error(`Error migrating record ${record._id}:`, error.message);
        }
      }
    }
    
    return results;
  }
  
  /**
   * Transform a source record to the unified schema format
   * @param {Object} record - Source record to transform
   * @returns {Object} Transformed record in unified schema format
   */
  async transformRecord(record) {
    // If a custom transformer is provided, use it
    if (this.transformer) {
      return this.transformer.transform(record, record.source || 'unknown');
    }
    
    // Otherwise use the frontend compatibility layer
    const unifiedData = convertFromLegacyListing(record);
    
    // Create a minimal property if conversion failed
    if (!unifiedData) {
      return createMinimalProperty({
        title: record.title,
        description: record.description,
        price: record.price,
        location: record.location,
        source: record.source,
        url: record.url,
        propertyType: record.propertyType || 'woning',
        size: record.size,
        rooms: record.rooms,
        bedrooms: record.bedrooms,
        year: record.year,
        interior: record.interior,
        images: record.images || [],
        dateAdded: record.dateAdded || record.timestamp
      });
    }
    
    return unifiedData;
  }
  
  /**
   * Validate the migration of a record
   * @param {Object} originalRecord - Original source record
   * @param {Object} transformedRecord - Transformed record
   * @returns {Object} Validation result with isValid flag and errors
   */
  async validateMigration(originalRecord, transformedRecord) {
    // If a custom validator is provided, use it
    if (this.validator) {
      return this.validator.validate(transformedRecord);
    }
    
    // Basic validation
    const errors = [];
    
    // Check required fields
    const requiredFields = ['title', 'price', 'location', 'source', 'url'];
    for (const field of requiredFields) {
      if (!transformedRecord[field]) {
        errors.push({
          field,
          message: `Required field '${field}' is missing or empty`
        });
      }
    }
    
    // Check data types
    if (transformedRecord.price && typeof transformedRecord.price !== 'string' && typeof transformedRecord.price !== 'number') {
      errors.push({
        field: 'price',
        message: 'Price must be a string or number'
      });
    }
    
    if (transformedRecord.images && !Array.isArray(transformedRecord.images)) {
      errors.push({
        field: 'images',
        message: 'Images must be an array'
      });
    }
    
    // Check data consistency
    if (originalRecord.title && transformedRecord.title && 
        originalRecord.title.trim() !== transformedRecord.title.trim()) {
      errors.push({
        field: 'title',
        message: 'Title does not match original record',
        original: originalRecord.title,
        transformed: transformedRecord.title
      });
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Create a target record from transformed data
   * @param {Object} sourceRecord - Original source record
   * @param {Object} transformedData - Transformed data
   * @param {Number} processingTime - Processing time in milliseconds
   * @param {Object} validationResult - Validation result
   * @returns {Object} Target record instance
   */
  async createTargetRecord(sourceRecord, transformedData, processingTime, validationResult) {
    // Create a new target record
    const targetRecord = await this.targetModel.createFromUnifiedSchema(transformedData);
    
    // Set migration metadata
    targetRecord.isLegacyMigrated = true;
    targetRecord.originalListingId = sourceRecord._id;
    
    // Set owner to system user if not provided
    if (!targetRecord.owner || !targetRecord.owner.userId) {
      targetRecord.owner = {
        userId: this.options.systemUserId,
        contactPreference: 'email'
      };
    }
    
    // Set status to active
    targetRecord.status = 'active';
    
    // Set source metadata
    targetRecord.sourceMetadata = {
      website: sourceRecord.source || 'unknown',
      externalId: null, // Not available in legacy records
      scraperId: null, // Not available in legacy records
      scrapedAt: sourceRecord.dateAdded || sourceRecord.timestamp || new Date(),
      lastUpdated: new Date(),
      version: 1
    };
    
    // Store raw data for reference
    targetRecord.rawData = {
      original: sourceRecord,
      processed: transformedData,
      metadata: { 
        migrated: true, 
        migratedAt: new Date(),
        migrationVersion: this.options.transformationVersion
      }
    };
    
    // Set data quality indicators
    const requiredFields = ['title', 'price', 'location', 'url', 'source'];
    const optionalFields = ['description', 'size', 'rooms', 'bedrooms', 'propertyType', 'year', 'interior', 'images'];
    
    const requiredFieldsPresent = requiredFields.filter(field => sourceRecord[field]).length;
    const optionalFieldsPresent = optionalFields.filter(field => {
      if (field === 'images') {
        return sourceRecord[field] && sourceRecord[field].length > 0;
      }
      return sourceRecord[field];
    }).length;
    
    const completeness = Math.round(
      ((requiredFieldsPresent / requiredFields.length) * 0.7 + 
       (optionalFieldsPresent / optionalFields.length) * 0.3) * 100
    );
    
    targetRecord.dataQuality = {
      completeness,
      accuracy: validationResult.isValid ? 80 : 60, // Lower accuracy if validation errors
      lastValidated: new Date(),
      validationErrors: validationResult.errors.map(e => e.message),
      validationWarnings: []
    };
    
    // Set processing metadata
    targetRecord.processingMetadata = {
      transformationVersion: this.options.transformationVersion,
      processingTime,
      processingDate: new Date(),
      errors: validationResult.errors.map(e => ({
        type: 'VALIDATION_ERROR',
        message: e.message,
        field: e.field,
        timestamp: new Date()
      })),
      warnings: []
    };
    
    return targetRecord;
  }
  
  /**
   * Roll back a migration
   * @param {Object} query - Optional query to filter records to roll back
   * @returns {Object} Rollback statistics
   */
  async rollbackMigration(query = {}) {
    try {
      console.log('Starting migration rollback...');
      
      // Find all migrated records
      const baseQuery = { isLegacyMigrated: true };
      const fullQuery = { ...baseQuery, ...query };
      
      const migratedRecords = await this.targetModel.find(fullQuery);
      console.log(`Found ${migratedRecords.length} migrated records to roll back`);
      
      let deletedCount = 0;
      let errorCount = 0;
      
      // Process in batches to avoid memory issues
      const batchSize = this.options.batchSize;
      const batches = Math.ceil(migratedRecords.length / batchSize);
      
      for (let i = 0; i < batches; i++) {
        const start = i * batchSize;
        const end = Math.min(start + batchSize, migratedRecords.length);
        const batch = migratedRecords.slice(start, end);
        
        console.log(`Processing rollback batch ${i + 1}/${batches} (${batch.length} records)`);
        
        for (const record of batch) {
          try {
            await this.targetModel.deleteOne({ _id: record._id });
            deletedCount++;
          } catch (error) {
            console.error(`Error deleting record ${record._id}:`, error);
            errorCount++;
          }
        }
      }
      
      console.log('Rollback completed');
      console.log(`- Total records: ${migratedRecords.length}`);
      console.log(`- Successfully deleted: ${deletedCount}`);
      console.log(`- Errors: ${errorCount}`);
      
      return {
        total: migratedRecords.length,
        deleted: deletedCount,
        errors: errorCount
      };
    } catch (error) {
      console.error('Rollback failed:', error);
      throw error;
    }
  }
  
  /**
   * Get migration statistics
   * @returns {Object} Migration statistics
   */
  getStats() {
    return { ...this.stats };
  }
  
  /**
   * Get migration history
   * @returns {Array} Migration history
   */
  getMigrationHistory() {
    return [...this.migrationHistory];
  }
}

module.exports = { MigrationService };