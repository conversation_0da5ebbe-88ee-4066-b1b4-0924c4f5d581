/**
 * Transformation Logger
 * 
 * This module provides specialized logging for transformation pipeline operations,
 * with detailed context tracking and performance metrics.
 */

const { loggers } = require('../services/logger');
const { transformationMonitor } = require('../monitoring/transformationMonitor');
const { performanceMonitor } = require('../monitoring/performanceMonitor');

/**
 * Transformation Logger Class
 * Provides specialized logging for transformation operations
 */
class TransformationLogger {
  /**
   * Create a new TransformationLogger
   */
  constructor() {
    this.logger = loggers.app.child({ component: 'transformation' });
  }

  /**
   * Log the start of a transformation operation
   * @param {string} source - Source identifier
   * @param {Object} options - Transformation options
   * @returns {Object} Transformation context for tracking
   */
  startTransformation(source, options = {}) {
    const context = transformationMonitor.startTransformation(source, options);
    
    this.logger.debug('Starting transformation', {
      transformationId: context.id,
      source,
      options
    });
    
    return context;
  }

  /**
   * Log the completion of a transformation operation
   * @param {Object} context - Transformation context from startTransformation
   * @param {Object} result - Transformation result
   * @param {Object} dataQuality - Data quality metrics
   * @returns {Promise<void>}
   */
  async endTransformation(context, result, dataQuality = null) {
    const perfData = await transformationMonitor.endTransformation(context, result, dataQuality);
    
    const logLevel = result && !result.error ? 'debug' : 'error';
    const logData = {
      transformationId: context.id,
      source: context.source,
      duration: `${perfData.duration}ms`,
      success: !result.error,
      memoryUsed: `${Math.round(perfData.memoryUsed / 1024 / 1024)}MB`
    };
    
    if (result.error) {
      logData.error = result.error;
    }
    
    if (dataQuality) {
      logData.dataQuality = dataQuality;
    }
    
    this.logger[logLevel]('Transformation completed', logData);
    
    // Record metrics in performance monitor
    performanceMonitor.recordTransformation({
      source: context.source,
      duration: perfData.duration,
      success: !result.error,
      cached: context.cached || false,
      quality: dataQuality
    });
    
    return perfData;
  }

  /**
   * Log a transformation error
   * @param {Object} context - Transformation context from startTransformation
   * @param {Error} error - Error that occurred
   * @returns {Promise<void>}
   */
  async logError(context, error) {
    const result = await transformationMonitor.recordError(context, error);
    
    this.logger.error('Transformation error', {
      transformationId: context.id,
      source: context.source,
      duration: `${result.duration}ms`,
      error: result.error
    });
    
    return result;
  }

  /**
   * Log batch transformation results
   * @param {Object} context - Transformation context from startTransformation
   * @param {Object} batchResult - Batch transformation result
   * @returns {Promise<void>}
   */
  async logBatchResults(context, batchResult) {
    const result = await transformationMonitor.recordBatchResults(context, batchResult);
    
    this.logger.info('Batch transformation completed', {
      transformationId: context.id,
      source: context.source,
      duration: `${result.duration}ms`,
      totalProcessed: batchResult.totalProcessed,
      successCount: batchResult.successCount,
      errorCount: batchResult.errorCount,
      memoryUsed: `${Math.round(result.memoryUsed / 1024 / 1024)}MB`
    });
    
    // Record batch metrics in performance monitor
    performanceMonitor.recordBatch({
      source: context.source,
      size: batchResult.totalProcessed,
      duration: result.duration,
      successful: batchResult.successCount,
      failed: batchResult.errorCount
    });
    
    return result;
  }

  /**
   * Log data quality metrics
   * @param {string} source - Source identifier
   * @param {Object} dataQuality - Data quality metrics
   */
  async logDataQuality(source, dataQuality) {
    await transformationMonitor.updateDataQuality(dataQuality);
    
    this.logger.info('Data quality metrics', {
      source,
      completeness: `${dataQuality.completeness}%`,
      accuracy: `${dataQuality.accuracy}%`,
      validationErrors: dataQuality.validationErrors || []
    });
  }

  /**
   * Log a field mapping operation
   * @param {string} source - Source identifier
   * @param {string} sourceField - Source field
   * @param {string} targetField - Target field
   * @param {*} sourceValue - Source value
   * @param {*} targetValue - Transformed target value
   * @param {boolean} success - Whether the mapping was successful
   * @param {Error} [error] - Error if mapping failed
   */
  logFieldMapping(source, sourceField, targetField, sourceValue, targetValue, success, error = null) {
    const logLevel = success ? 'debug' : 'warn';
    
    const logData = {
      source,
      sourceField,
      targetField,
      success
    };
    
    // Only include values in debug mode to avoid excessive logging
    if (this.logger.isDebugEnabled()) {
      logData.sourceValue = this._truncateValue(sourceValue);
      logData.targetValue = this._truncateValue(targetValue);
    }
    
    if (error) {
      logData.error = error.message;
    }
    
    this.logger[logLevel]('Field mapping', logData);
  }

  /**
   * Log a validation operation
   * @param {string} field - Field name
   * @param {*} value - Field value
   * @param {boolean} valid - Whether the field is valid
   * @param {string} [message] - Validation message if invalid
   */
  logValidation(field, value, valid, message = null) {
    const logLevel = valid ? 'debug' : 'warn';
    
    const logData = {
      field,
      valid
    };
    
    // Only include values in debug mode to avoid excessive logging
    if (this.logger.isDebugEnabled()) {
      logData.value = this._truncateValue(value);
    }
    
    if (message) {
      logData.message = message;
    }
    
    this.logger[logLevel]('Field validation', logData);
  }

  /**
   * Log a transformation function execution
   * @param {string} functionName - Transformation function name
   * @param {*} input - Input value
   * @param {*} output - Output value
   * @param {number} duration - Execution duration in milliseconds
   * @param {boolean} success - Whether the transformation was successful
   * @param {Error} [error] - Error if transformation failed
   */
  logTransformationFunction(functionName, input, output, duration, success, error = null) {
    const logLevel = success ? 'debug' : 'warn';
    
    const logData = {
      function: functionName,
      duration: `${duration}ms`,
      success
    };
    
    // Only include values in debug mode to avoid excessive logging
    if (this.logger.isDebugEnabled()) {
      logData.input = this._truncateValue(input);
      logData.output = this._truncateValue(output);
    }
    
    if (error) {
      logData.error = error.message;
    }
    
    this.logger[logLevel]('Transformation function', logData);
  }

  /**
   * Truncate a value for logging
   * @param {*} value - Value to truncate
   * @returns {*} Truncated value
   * @private
   */
  _truncateValue(value) {
    if (value === null || value === undefined) {
      return value;
    }
    
    if (typeof value === 'string') {
      return value.length > 100 ? `${value.substring(0, 100)}...` : value;
    }
    
    if (typeof value === 'object') {
      try {
        const json = JSON.stringify(value);
        return json.length > 200 ? `${json.substring(0, 200)}...` : json;
      } catch (e) {
        return '[Complex Object]';
      }
    }
    
    return value;
  }
  
  /**
   * Log a cache hit
   * @param {string} operation - Operation type
   * @param {string} source - Source identifier
   */
  logCacheHit(operation, source) {
    this.logger.debug('Cache hit', {
      operation,
      source
    });
    
    // Mark the context as cached for performance monitoring
    return { cached: true };
  }
  
  /**
   * Log a cache store operation
   * @param {string} operation - Operation type
   * @param {string} source - Source identifier
   */
  logCacheStore(operation, source) {
    this.logger.debug('Cache store', {
      operation,
      source
    });
  }
}

// Create singleton instance
const transformationLogger = new TransformationLogger();

module.exports = {
  transformationLogger,
  TransformationLogger
};