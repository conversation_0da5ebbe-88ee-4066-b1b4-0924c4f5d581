const mongoose = require('mongoose');

/**
 * Migration script to update existing users with default values for new enhanced profile fields
 * This ensures backward compatibility when the User model is extended
 */

async function migrateUsers() {
  try {
    console.log('Starting user model enhancement migration...');
    
    // Connect to MongoDB if not already connected
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/zakmakelaar');
    }
    
    const User = mongoose.model('User');
    
    // Find all users that don't have the new fields
    const usersToUpdate = await User.find({
      $or: [
        { 'profile.userType': { $exists: false } },
        { 'tenantScore': { $exists: false } },
        { 'notifications': { $exists: false } },
        { 'activity': { $exists: false } },
        { 'emailVerified': { $exists: false } }
      ]
    });
    
    console.log(`Found ${usersToUpdate.length} users to migrate`);
    
    for (const user of usersToUpdate) {
      const updateFields = {};
      
      // Add default values for new fields if they don't exist
      if (!user.emailVerified) {
        updateFields.emailVerified = false;
      }
      
      if (!user.profile.userType) {
        updateFields['profile.userType'] = [];
      }
      
      if (!user.profile.employment) {
        updateFields['profile.employment'] = {
          incomeVerified: false,
          incomeDocuments: []
        };
      }
      
      if (!user.profile.socialPreferences) {
        updateFields['profile.socialPreferences'] = {
          lookingForRoommate: false,
          roommateCriteria: {
            gender: 'any',
            lifestyle: {
              cleanliness: 'moderate',
              noiseLevel: 'moderate',
              socialLevel: 'moderate',
              smokingTolerance: false,
              petTolerance: false,
              guestPolicy: 'moderate'
            }
          },
          isVisible: false
        };
      }
      
      if (!user.tenantScore) {
        updateFields.tenantScore = {
          overallScore: 0,
          components: {
            incomeStability: 0,
            rentalHistory: 0,
            creditworthiness: 0,
            employment: 0,
            references: 0
          },
          verificationLevel: 'unverified'
        };
      }
      
      if (!user.documents) {
        updateFields.documents = [];
      }
      
      if (!user.propertyOwner) {
        updateFields.propertyOwner = {
          isPropertyOwner: false,
          properties: [],
          verificationStatus: 'pending'
        };
      }
      
      if (!user.notifications) {
        updateFields.notifications = {
          email: {
            newListings: true,
            priceChanges: true,
            applicationUpdates: true,
            socialMatches: true
          },
          sms: {
            urgentAlerts: false,
            viewingReminders: false
          },
          push: {
            newMatches: true,
            messages: true,
            systemUpdates: false
          }
        };
      }
      
      if (!user.activity) {
        updateFields.activity = {
          loginCount: 0,
          searchHistory: [],
          applicationsSent: 0,
          viewingsAttended: 0
        };
      }
      
      // Update AI settings with new fields
      if (user.aiSettings) {
        if (!user.aiSettings.tenantScoreVisible) {
          updateFields['aiSettings.tenantScoreVisible'] = true;
        }
        if (!user.aiSettings.socialMatchingEnabled) {
          updateFields['aiSettings.socialMatchingEnabled'] = false;
        }
        if (!user.aiSettings.autoApplyEnabled) {
          updateFields['aiSettings.autoApplyEnabled'] = false;
        }
        if (!user.aiSettings.fakeListingDetection) {
          updateFields['aiSettings.fakeListingDetection'] = true;
        }
      }
      
      // Migrate old profile fields to new structure
      if (user.profile.occupation && !user.profile.employment?.occupation) {
        updateFields['profile.employment.occupation'] = user.profile.occupation;
      }
      
      if (user.profile.income && !user.profile.employment?.monthlyIncome) {
        updateFields['profile.employment.monthlyIncome'] = user.profile.income;
      }
      
      // Update the user with new fields
      if (Object.keys(updateFields).length > 0) {
        await User.updateOne({ _id: user._id }, { $set: updateFields });
        console.log(`Updated user ${user.email} with new fields`);
      }
    }
    
    console.log('Migration completed successfully');
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateUsers()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateUsers };