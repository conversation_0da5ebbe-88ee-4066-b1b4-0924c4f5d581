/**
 * Schema Error Classification System
 * 
 * This module provides error types and classes for handling schema-related errors
 * in a consistent way across the application.
 */

/**
 * Error types for schema-related errors
 */
const ErrorTypes = {
  TRANSFORMATION_ERROR: 'TRANSFORMATION_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  MAPPING_ERROR: 'MAPPING_ERROR',
  DATA_QUALITY_ERROR: 'DATA_QUALITY_ERROR'
};

/**
 * Schema Error class for schema-related errors
 * @extends Error
 */
class SchemaError extends Error {
  /**
   * Create a new SchemaError
   * @param {string} type - Error type from ErrorTypes
   * @param {string} message - Error message
   * @param {Object} context - Additional context information
   */
  constructor(type, message, context = {}) {
    super(message);
    this.name = 'SchemaError';
    this.type = type;
    this.context = context;
    this.timestamp = new Date();
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, SchemaError);
    }
  }

  /**
   * Convert error to JSON-serializable object
   * @returns {Object} JSON-serializable representation of the error
   */
  toJSON() {
    return {
      name: this.name,
      type: this.type,
      message: this.message,
      context: this.context,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }

  /**
   * Create a transformation error
   * @param {string} message - Error message
   * @param {Object} context - Additional context information
   * @returns {SchemaError} Schema error instance
   */
  static transformationError(message, context = {}) {
    return new SchemaError(ErrorTypes.TRANSFORMATION_ERROR, message, context);
  }

  /**
   * Create a validation error
   * @param {string} message - Error message
   * @param {Object} context - Additional context information
   * @returns {SchemaError} Schema error instance
   */
  static validationError(message, context = {}) {
    return new SchemaError(ErrorTypes.VALIDATION_ERROR, message, context);
  }

  /**
   * Create a mapping error
   * @param {string} message - Error message
   * @param {Object} context - Additional context information
   * @returns {SchemaError} Schema error instance
   */
  static mappingError(message, context = {}) {
    return new SchemaError(ErrorTypes.MAPPING_ERROR, message, context);
  }

  /**
   * Create a data quality error
   * @param {string} message - Error message
   * @param {Object} context - Additional context information
   * @returns {SchemaError} Schema error instance
   */
  static dataQualityError(message, context = {}) {
    return new SchemaError(ErrorTypes.DATA_QUALITY_ERROR, message, context);
  }
}

module.exports = {
  ErrorTypes,
  SchemaError
};