const User = require("../models/User");
const Listing = require("../models/Listing");
const config = require("../config/config");
const aiService = require("./aiService");

// Lazy initialization of external services to avoid errors when credentials are not configured
let sgMail = null;
let twilioClient = null;

// Initialize SendGrid only if API key is provided
const initSendGrid = () => {
  if (!sgMail && config.sendgridApiKey) {
    sgMail = require("@sendgrid/mail");
    sgMail.setApiKey(config.sendgridApiKey);
  }
  return sgMail;
};

// Initialize Twilio only if credentials are provided
const initTwilio = () => {
  if (!twilioClient && config.twilioAccountSid && config.twilioAuthToken) {
    const twilio = require("twilio");
    twilioClient = twilio(config.twilioAccountSid, config.twilioAuthToken);
  }
  return twilioClient;
};

const sendAlerts = async (listing) => {
  const users = await User.find();

  for (const user of users) {
    try {
      // Use AI for smarter matching
      const matchResult = await aiService.matchListingToUser(
        listing,
        user.preferences
      );

      // Check if match score meets user's threshold
      if (matchResult.score >= (user.aiSettings?.matchThreshold || 70)) {
        await sendPersonalizedAlert(user, listing, matchResult);
      }
    } catch (error) {
      console.error(`Error processing alert for user ${user.email}:`, error);
      // Fallback to basic matching if AI fails
      if (matchesBasicPreferences(listing, user.preferences)) {
        await sendBasicAlert(user, listing);
      }
    }
  }
};

const sendPersonalizedAlert = async (user, listing, matchResult) => {
  const { aiSettings } = user;
  const language = aiSettings?.preferredLanguage || "english";

  try {
    // Generate personalized summary if AI is available
    const summary = await aiService.summarizeListing(listing, language);

    // Generate market analysis if enabled
    let marketAnalysis = null;
    if (aiSettings?.includeMarketAnalysis) {
      try {
        marketAnalysis = await aiService.analyzeMarketTrends(
          listing.location,
          listing.propertyType,
          {} // historical data would be passed here
        );
      } catch (error) {
        console.error("Market analysis failed:", error);
      }
    }

    // Send email alert
    await sendEnhancedEmail(
      user.email,
      listing,
      matchResult,
      summary,
      marketAnalysis
    );

    // Send WhatsApp alert
    await sendEnhancedWhatsApp(
      user.profile?.phone,
      listing,
      matchResult,
      summary
    );
  } catch (error) {
    console.error("Personalized alert generation failed:", error);
    // Fallback to basic alert
    await sendBasicAlert(user, listing);
  }
};

const sendEnhancedEmail = async (
  to,
  listing,
  matchResult,
  summary,
  marketAnalysis
) => {
  const sgMailClient = initSendGrid();

  if (!sgMailClient) {
    console.log("SendGrid not configured, skipping email notification");
    return;
  }

  let emailContent = `
    <h2>🏠 New Listing Match: ${listing.title}</h2>
    <p><strong>Match Score:</strong> ${matchResult.score}/100</p>
    <p><strong>Recommendation:</strong> ${matchResult.recommendation}</p>
    
    <h3>Property Details:</h3>
    <ul>
      <li><strong>Price:</strong> ${listing.price}</li>
      <li><strong>Location:</strong> ${listing.location}</li>
      <li><strong>Size:</strong> ${listing.size}</li>
      <li><strong>Rooms:</strong> ${listing.rooms}</li>
    </ul>
    
    <h3>AI Analysis:</h3>
    <p>${matchResult.matchReasoning}</p>
    
    <h3>Key Highlights:</h3>
    <ul>
      ${matchResult.keyHighlights
        .map((highlight) => `<li>${highlight}</li>`)
        .join("")}
    </ul>
  `;

  if (summary) {
    emailContent += `
      <h3>Smart Summary:</h3>
      <p>${summary.summary}</p>
    `;
  }

  if (marketAnalysis) {
    emailContent += `
      <h3>Market Insights:</h3>
      <p><strong>Trend:</strong> ${marketAnalysis.marketTrend}</p>
      <p><strong>Demand:</strong> ${marketAnalysis.demandLevel}</p>
      <p><strong>Prediction:</strong> ${marketAnalysis.pricePrediction}</p>
    `;
  }

  if (matchResult.potentialConcerns.length > 0) {
    emailContent += `
      <h3>⚠️ Potential Concerns:</h3>
      <ul>
        ${matchResult.potentialConcerns
          .map((concern) => `<li>${concern}</li>`)
          .join("")}
      </ul>
    `;
  }

  emailContent += `
    <p><a href="${listing.url}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Listing</a></p>
  `;

  const msg = {
    to,
    from: config.sendgridFromEmail,
    subject: `🎯 Perfect Match Found: ${listing.title} (${matchResult.score}/100)`,
    html: emailContent,
  };

  sgMailClient
    .send(msg)
    .catch((error) => console.error("SendGrid error:", error));
};

const sendEnhancedWhatsApp = async (to, listing, matchResult, summary) => {
  if (!to) return;

  const twilioClientInstance = initTwilio();

  if (!twilioClientInstance) {
    console.log("Twilio not configured, skipping WhatsApp notification");
    return;
  }

  let message = `🏠 *New Perfect Match!*\n\n`;
  message += `*${listing.title}*\n`;
  message += `📍 ${listing.location}\n`;
  message += `💰 ${listing.price}\n`;
  message += `📊 Match Score: ${matchResult.score}/100\n`;
  message += `⭐ Recommendation: ${matchResult.recommendation}\n\n`;

  if (summary) {
    message += `📝 *Summary:* ${summary.summary}\n\n`;
  }

  message += `🔗 ${listing.url}`;

  twilioClientInstance.messages
    .create({
      body: message,
      from: config.twilioWhatsAppFrom,
      to: `whatsapp:${to}`,
    })
    .catch((error) => console.error("Twilio error:", error));
};

const sendBasicAlert = async (user, listing) => {
  await sendEmail(user.email, listing);
  await sendWhatsApp(user.profile?.phone, listing);
};

// Legacy basic preference matching (fallback)
const matchesBasicPreferences = (listing, preferences) => {
  if (!preferences) return false;

  const { location, budget, rooms } = preferences;

  if (
    location &&
    !listing.location.toLowerCase().includes(location.toLowerCase())
  ) {
    return false;
  }

  if (budget && listing.price > budget) {
    return false;
  }

  if (rooms && listing.size && !listing.size.includes(rooms)) {
    return false;
  }

  return true;
};

// Legacy email function (kept for fallback)
const sendEmail = (to, listing) => {
  const sgMailClient = initSendGrid();

  if (!sgMailClient) {
    console.log("SendGrid not configured, skipping email notification");
    return;
  }

  const msg = {
    to,
    from: config.sendgridFromEmail,
    subject: `New Listing: ${listing.title}`,
    html: `<p>A new listing matching your preferences is available:</p>
           <p><strong>Title:</strong> ${listing.title}</p>
           <p><strong>Price:</strong> ${listing.price}</p>
           <p><strong>Location:</strong> ${listing.location}</p>
           <p><a href="${listing.url}">View Listing</a></p>`,
  };

  sgMailClient
    .send(msg)
    .catch((error) => console.error("SendGrid error:", error));
};

// Legacy WhatsApp function (kept for fallback)
const sendWhatsApp = (to, listing) => {
  if (!to) return;

  const twilioClientInstance = initTwilio();

  if (!twilioClientInstance) {
    console.log("Twilio not configured, skipping WhatsApp notification");
    return;
  }

  const message = `New Listing: ${listing.title}\nPrice: ${listing.price}\nLocation: ${listing.location}\n${listing.url}`;

  twilioClientInstance.messages
    .create({
      body: message,
      from: config.twilioWhatsAppFrom,
      to: `whatsapp:${to}`,
    })
    .catch((error) => console.error("Twilio error:", error));
};

module.exports = { sendAlerts };
