/**
 * Migration script to enhance the Property model with unified schema support
 * 
 * This script adds the new unified schema fields to existing Property documents
 * and creates indexes for efficient querying.
 */

const mongoose = require('mongoose');
const { MongoClient } = require('mongodb');
require('dotenv').config();

async function migratePropertyModel() {
  try {
    console.log('Starting Property model enhancement migration...');
    
    // Connect to MongoDB
    const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/zakmakelaar';
    const client = new MongoClient(uri);
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    const propertyCollection = db.collection('properties');
    
    // 1. Add new fields to all existing properties
    console.log('Adding unified schema fields to existing properties...');
    
    const updateResult = await propertyCollection.updateMany(
      {}, // Match all documents
      {
        $set: {
          unifiedData: {},
          sourceMetadata: {
            website: null,
            externalId: null,
            scraperId: null,
            scrapedAt: new Date(),
            lastUpdated: new Date(),
            version: 1
          },
          dataQuality: {
            completeness: 0,
            accuracy: 0,
            lastValidated: null,
            validationErrors: [],
            validationWarnings: []
          },
          processingMetadata: {
            transformationVersion: '1.0.0',
            processingTime: 0,
            processingDate: new Date(),
            errors: [],
            warnings: []
          },
          rawData: {
            original: null,
            processed: null,
            metadata: null
          },
          isLegacyMigrated: false,
          originalListingId: null
        }
      },
      { upsert: false }
    );
    
    console.log(`Updated ${updateResult.modifiedCount} properties with unified schema fields`);
    
    // 2. Create indexes for efficient querying
    console.log('Creating indexes for unified schema fields...');
    
    await propertyCollection.createIndex({ 'sourceMetadata.website': 1 });
    await propertyCollection.createIndex(
      { 'sourceMetadata.externalId': 1, 'sourceMetadata.website': 1 },
      { unique: true, sparse: true }
    );
    await propertyCollection.createIndex({ 'dataQuality.completeness': 1 });
    await propertyCollection.createIndex({ 'dataQuality.accuracy': 1 });
    await propertyCollection.createIndex({ 'sourceMetadata.scrapedAt': -1 });
    await propertyCollection.createIndex({ 'processingMetadata.processingDate': -1 });
    await propertyCollection.createIndex({ isLegacyMigrated: 1 });
    
    // Compound indexes
    await propertyCollection.createIndex({ 'sourceMetadata.website': 1, 'dataQuality.completeness': -1 });
    await propertyCollection.createIndex({ 'sourceMetadata.website': 1, 'sourceMetadata.scrapedAt': -1 });
    
    console.log('Successfully created indexes for unified schema fields');
    
    // Close the connection
    await client.close();
    console.log('Migration completed successfully');
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Run migration if called directly
if (require.main === module) {
  migratePropertyModel()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migratePropertyModel };