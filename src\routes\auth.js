const express = require("express");
const userController = require("../controllers/userController");
const { auth } = require("../middleware/auth");
const { authLimiter } = require("../middleware/rateLimiter");
const {
  validateUserRegistration,
  validateUserLogin,
  validateUserPreferences,
  validateUserId,
  validateProfileCompletion,
  validateUserType,
  validateLanguagePreference,
  validateEmployment,
  validateSocialPreferences,
  validateRentalHistory,
} = require("../middleware/validation");

const router = express.Router();

// Apply auth rate limiting to all routes in this router
router.use(authLimiter);

// Get current authenticated user
router.get("/me", auth, userController.getMe);

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique user identifier
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         preferences:
 *           type: object
 *           properties:
 *             location:
 *               type: string
 *               description: Preferred location for listings
 *             budget:
 *               type: number
 *               description: Budget for property search
 *             rooms:
 *               type: number
 *               description: Preferred number of rooms
 *         profile:
 *           type: object
 *           properties:
 *             name:
 *               type: string
 *               description: User's full name
 *             income:
 *               type: number
 *               description: User's income
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Account creation date
 *
 *     AuthResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           example: success
 *         token:
 *           type: string
 *           description: JWT authentication token
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               $ref: '#/components/schemas/User'
 *
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *
 * tags:
 *   name: Authentication
 *   description: User authentication and management
 */

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address (must be unique)
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 format: password
 *                 minLength: 8
 *                 description: Password (min 8 chars, must contain uppercase, lowercase, and number)
 *                 example: MyPassword123
 *     responses:
 *       201:
 *         description: User registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Validation failed or user already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: User with this email already exists
 *       429:
 *         description: Too many registration attempts
 *       500:
 *         description: Internal server error
 */
router.post("/register", validateUserRegistration, userController.register);

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Log in a user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 format: password
 *     responses:
 *       200:
 *         description: User logged in successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Validation failed
 *       401:
 *         description: Invalid credentials
 *       429:
 *         description: Too many login attempts
 *       500:
 *         description: Internal server error
 */
router.post("/login", validateUserLogin, userController.login);

// Logout user
router.post("/logout", (req, res) => {
  res.status(200).json({
    status: "success",
    message: "Logged out successfully",
  });
});

/**
 * @swagger
 * /api/auth/users/{userId}/preferences:
 *   put:
 *     summary: Update user preferences
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The user ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               preferences:
 *                 type: object
 *                 properties:
 *                   location:
 *                     type: string
 *                     description: Preferred location for property search
 *                     example: Amsterdam
 *                   budget:
 *                     type: number
 *                     description: Budget for property search
 *                     example: 2000
 *                   rooms:
 *                     type: number
 *                     description: Preferred number of rooms
 *                     example: 3
 *               profile:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     description: User's full name
 *                     example: John Doe
 *                   income:
 *                     type: number
 *                     description: User's income
 *                     example: 50000
 *     responses:
 *       200:
 *         description: User preferences updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *       400:
 *         description: Validation failed
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.put(
  "/users/:userId/preferences",
  validateUserId,
  auth,
  validateUserPreferences,
  userController.updatePreferences
);

// Enhanced Authentication Routes

/**
 * @swagger
 * /api/auth/profile/complete:
 *   post:
 *     summary: Complete user profile with enhanced information
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - lastName
 *               - userType
 *             properties:
 *               firstName:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 50
 *                 example: John
 *               lastName:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 50
 *                 example: Doe
 *               userType:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [student, expat, young_professional, property_owner]
 *                 minItems: 1
 *                 example: [young_professional]
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *                 example: 1990-01-01
 *               nationality:
 *                 type: string
 *                 example: Dutch
 *               phoneNumber:
 *                 type: string
 *                 example: +31612345678
 *               employment:
 *                 type: object
 *                 properties:
 *                   occupation:
 *                     type: string
 *                     example: Software Engineer
 *                   employmentType:
 *                     type: string
 *                     enum: [full-time, part-time, student, freelancer, unemployed]
 *                   monthlyIncome:
 *                     type: number
 *                     example: 4000
 *     responses:
 *       200:
 *         description: Profile completed successfully
 *       400:
 *         description: Validation failed
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 */
router.post(
  "/profile/complete",
  auth,
  validateProfileCompletion,
  userController.completeProfile
);

/**
 * @swagger
 * /api/auth/profile/status:
 *   get:
 *     summary: Get user profile completion status
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Profile status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     isProfileComplete:
 *                       type: boolean
 *                     missingFields:
 *                       type: array
 *                       items:
 *                         type: string
 *                     completionPercentage:
 *                       type: number
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 */
router.get("/profile/status", auth, userController.getProfileStatus);

/**
 * @swagger
 * /api/auth/user-type:
 *   put:
 *     summary: Update user type
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userType
 *             properties:
 *               userType:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [student, expat, young_professional, property_owner]
 *                 minItems: 1
 *                 example: [student, expat]
 *     responses:
 *       200:
 *         description: User type updated successfully
 *       400:
 *         description: Validation failed
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 */
router.put("/user-type", auth, validateUserType, userController.updateUserType);

/**
 * @swagger
 * /api/auth/language:
 *   put:
 *     summary: Set language preference
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - language
 *             properties:
 *               language:
 *                 type: string
 *                 enum: [dutch, english, arabic, turkish, polish]
 *                 example: english
 *     responses:
 *       200:
 *         description: Language preference updated successfully
 *       400:
 *         description: Validation failed
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 */
router.put(
  "/language",
  auth,
  validateLanguagePreference,
  userController.setLanguagePreference
);

/**
 * @swagger
 * /api/auth/profile-picture:
 *   post:
 *     summary: Upload profile picture
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               profilePicture:
 *                 type: string
 *                 format: binary
 *                 description: Profile picture image file (max 5MB, images only)
 *     responses:
 *       200:
 *         description: Profile picture uploaded successfully
 *       400:
 *         description: Invalid file or validation failed
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 */
router.post(
  "/profile-picture",
  auth,
  userController.uploadProfilePicture,
  userController.updateProfilePicture
);

/**
 * @swagger
 * /api/auth/employment:
 *   put:
 *     summary: Update employment information
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               employment:
 *                 type: object
 *                 properties:
 *                   occupation:
 *                     type: string
 *                     example: Software Engineer
 *                   employmentType:
 *                     type: string
 *                     enum: [full-time, part-time, student, freelancer, unemployed]
 *                   contractType:
 *                     type: string
 *                     enum: [permanent, temporary, student, freelancer]
 *                   employer:
 *                     type: string
 *                     example: Tech Company B.V.
 *                   workLocation:
 *                     type: string
 *                     example: Amsterdam
 *                   monthlyIncome:
 *                     type: number
 *                     example: 4000
 *     responses:
 *       200:
 *         description: Employment information updated successfully
 *       400:
 *         description: Validation failed
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 */
router.put(
  "/employment",
  auth,
  validateEmployment,
  userController.updateEmployment
);

/**
 * @swagger
 * /api/auth/social-preferences:
 *   put:
 *     summary: Update social matching preferences
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               socialPreferences:
 *                 type: object
 *                 properties:
 *                   lookingForRoommate:
 *                     type: boolean
 *                     example: true
 *                   roommateCriteria:
 *                     type: object
 *                     properties:
 *                       ageRange:
 *                         type: object
 *                         properties:
 *                           min:
 *                             type: integer
 *                             minimum: 18
 *                             maximum: 100
 *                             example: 25
 *                           max:
 *                             type: integer
 *                             minimum: 18
 *                             maximum: 100
 *                             example: 35
 *                       gender:
 *                         type: string
 *                         enum: [male, female, any]
 *                         example: any
 *                       lifestyle:
 *                         type: object
 *                         properties:
 *                           cleanliness:
 *                             type: string
 *                             enum: [very_clean, clean, moderate, relaxed]
 *                           noiseLevel:
 *                             type: string
 *                             enum: [very_quiet, quiet, moderate, lively]
 *                           socialLevel:
 *                             type: string
 *                             enum: [very_social, social, moderate, private]
 *                           smokingTolerance:
 *                             type: boolean
 *                           petTolerance:
 *                             type: boolean
 *                           guestPolicy:
 *                             type: string
 *                             enum: [strict, moderate, flexible]
 *                   isVisible:
 *                     type: boolean
 *                     example: true
 *     responses:
 *       200:
 *         description: Social preferences updated successfully
 *       400:
 *         description: Validation failed
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 */
router.put(
  "/social-preferences",
  auth,
  validateSocialPreferences,
  userController.updateSocialPreferences
);

/**
 * @swagger
 * /api/auth/rental-history:
 *   put:
 *     summary: Update rental history information
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               rentalHistory:
 *                 type: object
 *                 properties:
 *                   previousAddresses:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         address:
 *                           type: string
 *                           example: Damrak 1, Amsterdam
 *                         landlordName:
 *                           type: string
 *                           example: John Smith
 *                         landlordContact:
 *                           type: string
 *                           example: <EMAIL>
 *                         rentAmount:
 *                           type: number
 *                           example: 1500
 *                         duration:
 *                           type: string
 *                           example: 2 years
 *                         reasonForLeaving:
 *                           type: string
 *                           example: Job relocation
 *                   evictions:
 *                     type: boolean
 *                     example: false
 *                   paymentIssues:
 *                     type: boolean
 *                     example: false
 *                   creditScore:
 *                     type: string
 *                     enum: [excellent, good, fair, poor]
 *                     example: good
 *     responses:
 *       200:
 *         description: Rental history updated successfully
 *       400:
 *         description: Validation failed
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 */
router.put("/rental-history", auth, validateRentalHistory, userController.updateRentalHistory);

/**
 * @swagger
 * /api/auth/tenant-score:
 *   get:
 *     summary: Get current tenant score
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Tenant score retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     tenantScore:
 *                       type: object
 *                       properties:
 *                         overallScore:
 *                           type: number
 *                           example: 85
 *                         components:
 *                           type: object
 *                           properties:
 *                             incomeStability:
 *                               type: number
 *                               example: 90
 *                             rentalHistory:
 *                               type: number
 *                               example: 80
 *                             creditworthiness:
 *                               type: number
 *                               example: 85
 *                             employment:
 *                               type: number
 *                               example: 95
 *                             references:
 *                               type: number
 *                               example: 70
 *                         verificationLevel:
 *                           type: string
 *                           enum: [unverified, partial, verified]
 *                           example: partial
 *                         lastCalculated:
 *                           type: string
 *                           format: date-time
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Error calculating tenant score
 */
router.get("/tenant-score", auth, userController.getTenantScore);

/**
 * @swagger
 * /api/auth/tenant-score/breakdown:
 *   get:
 *     summary: Get detailed tenant score breakdown
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Detailed score breakdown retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     breakdown:
 *                       type: object
 *                       properties:
 *                         overallScore:
 *                           type: number
 *                           example: 85
 *                         grade:
 *                           type: string
 *                           example: B
 *                         breakdown:
 *                           type: object
 *                           description: Detailed breakdown of each component
 *                         recommendations:
 *                           type: array
 *                           items:
 *                             type: string
 *                           example: ["Verify your income with official documents"]
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Error getting score breakdown
 */
router.get("/tenant-score/breakdown", auth, userController.getTenantScoreBreakdown);

/**
 * @swagger
 * /api/auth/tenant-score/report:
 *   get:
 *     summary: Generate comprehensive tenant score report
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Tenant score report generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     report:
 *                       type: object
 *                       properties:
 *                         userId:
 *                           type: string
 *                         userName:
 *                           type: string
 *                         generatedAt:
 *                           type: string
 *                           format: date-time
 *                         score:
 *                           type: number
 *                         grade:
 *                           type: string
 *                         summary:
 *                           type: object
 *                           properties:
 *                             strengths:
 *                               type: array
 *                               items:
 *                                 type: string
 *                             weaknesses:
 *                               type: array
 *                               items:
 *                                 type: string
 *                             improvementAreas:
 *                               type: array
 *                               items:
 *                                 type: object
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Error generating score report
 */
router.get("/tenant-score/report", auth, userController.generateTenantScoreReport);

/**
 * @swagger
 * /api/auth/change-password:
 *   put:
 *     summary: Change user password
 *     description: Change the current user's password
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *                 description: Current password
 *               newPassword:
 *                 type: string
 *                 minLength: 6
 *                 description: New password (minimum 6 characters)
 *     responses:
 *       200:
 *         description: Password changed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Password changed successfully
 *       400:
 *         description: Validation error or incorrect current password
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Current password is incorrect
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put("/change-password", auth, userController.changePassword);

module.exports = router;
