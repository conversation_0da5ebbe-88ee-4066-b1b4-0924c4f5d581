const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const documentVaultService = require('../services/documentVaultService');
const rateLimit = require('express-rate-limit');

// Rate limiting for document operations
const documentUploadLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Maximum 10 uploads per 15 minutes
  message: 'Too many document uploads, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
});

const documentDownloadLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Maximum 50 downloads per 15 minutes
  message: 'Too many document downloads, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware to check admin role
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return next(new AppError('Admin access required', 403));
  }
  next();
};

// Get upload middleware
const upload = documentVaultService.getUploadMiddleware();

/**
 * @swagger
 * /api/documents/upload:
 *   post:
 *     summary: Upload a document
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               documents:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *               type:
 *                 type: string
 *                 enum: [income_proof, employment_contract, bank_statement, id_document, rental_reference, other]
 *               expiryDate:
 *                 type: string
 *                 format: date
 *     responses:
 *       201:
 *         description: Documents uploaded successfully
 *       400:
 *         description: Invalid file or parameters
 *       401:
 *         description: Unauthorized
 */
router.post('/upload', 
  auth, 
  documentUploadLimit,
  upload.array('documents', 5),
  catchAsync(async (req, res, next) => {
    const { type, expiryDate } = req.body;
    const files = req.files;

    if (!files || files.length === 0) {
      return next(new AppError('No files uploaded', 400));
    }

    if (!type) {
      return next(new AppError('Document type is required', 400));
    }

    const uploadedDocuments = [];
    const errors = [];

    // Process each uploaded file
    for (const file of files) {
      try {
        const document = await documentVaultService.uploadDocument(
          req.user._id,
          file,
          type,
          {
            expiryDate,
            ipAddress: req.ip
          }
        );
        uploadedDocuments.push(document);
      } catch (error) {
        errors.push({
          filename: file.originalname,
          error: error.message
        });
      }
    }

    if (uploadedDocuments.length === 0) {
      return next(new AppError('All uploads failed', 400));
    }

    const response = {
      status: 'success',
      message: `${uploadedDocuments.length} document(s) uploaded successfully`,
      data: {
        documents: uploadedDocuments
      }
    };

    if (errors.length > 0) {
      response.warnings = errors;
    }

    res.status(201).json(response);
  })
);

/**
 * @swagger
 * /api/documents:
 *   get:
 *     summary: Get user's documents
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [income_proof, employment_contract, bank_statement, id_document, rental_reference, other]
 *         description: Filter by document type
 *     responses:
 *       200:
 *         description: Documents retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/', 
  auth,
  catchAsync(async (req, res, next) => {
    const { type } = req.query;
    
    const documents = await documentVaultService.getDocuments(req.user._id, type);
    
    res.status(200).json({
      status: 'success',
      data: {
        documents,
        count: documents.length
      }
    });
  })
);

/**
 * @swagger
 * /api/documents/{documentId}/download:
 *   get:
 *     summary: Download a document
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: documentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Document ID
 *     responses:
 *       200:
 *         description: Document downloaded successfully
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *       403:
 *         description: Access denied
 *       404:
 *         description: Document not found
 */
router.get('/:documentId/download',
  auth,
  documentDownloadLimit,
  catchAsync(async (req, res, next) => {
    const { documentId } = req.params;
    
    const fileData = await documentVaultService.downloadDocument(
      documentId,
      req.user._id,
      req.user.role,
      req.ip
    );
    
    res.set({
      'Content-Type': fileData.mimeType,
      'Content-Disposition': `attachment; filename="${fileData.filename}"`,
      'Content-Length': fileData.size
    });
    
    res.send(fileData.data);
  })
);

/**
 * @swagger
 * /api/documents/{documentId}:
 *   delete:
 *     summary: Delete a document
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: documentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Document ID
 *     responses:
 *       200:
 *         description: Document deleted successfully
 *       403:
 *         description: Access denied
 *       404:
 *         description: Document not found
 */
router.delete('/:documentId',
  auth,
  catchAsync(async (req, res, next) => {
    const { documentId } = req.params;
    
    await documentVaultService.deleteDocument(
      documentId,
      req.user._id,
      req.user.role,
      req.ip
    );
    
    res.status(200).json({
      status: 'success',
      message: 'Document deleted successfully'
    });
  })
);

/**
 * @swagger
 * /api/documents/report:
 *   get:
 *     summary: Get document report for user
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Document report generated successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/report',
  auth,
  catchAsync(async (req, res, next) => {
    const report = await documentVaultService.generateDocumentReport(req.user._id);
    
    res.status(200).json({
      status: 'success',
      data: {
        report
      }
    });
  })
);

// Admin routes

/**
 * @swagger
 * /api/documents/admin/unverified:
 *   get:
 *     summary: Get unverified documents (Admin only)
 *     tags: [Documents, Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Maximum number of documents to return
 *     responses:
 *       200:
 *         description: Unverified documents retrieved successfully
 *       403:
 *         description: Admin access required
 */
router.get('/admin/unverified',
  auth,
  requireAdmin,
  catchAsync(async (req, res, next) => {
    const { limit = 50 } = req.query;
    
    const documents = await documentVaultService.getUnverifiedDocuments(parseInt(limit));
    
    res.status(200).json({
      status: 'success',
      data: {
        documents,
        count: documents.length
      }
    });
  })
);

/**
 * @swagger
 * /api/documents/admin/{documentId}/verify:
 *   post:
 *     summary: Verify a document (Admin only)
 *     tags: [Documents, Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: documentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Document ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               verified:
 *                 type: boolean
 *                 description: Verification status
 *               notes:
 *                 type: string
 *                 description: Optional verification notes
 *     responses:
 *       200:
 *         description: Document verification updated successfully
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Document not found
 */
router.post('/admin/:documentId/verify',
  auth,
  requireAdmin,
  catchAsync(async (req, res, next) => {
    const { documentId } = req.params;
    const { verified, notes } = req.body;
    
    if (typeof verified !== 'boolean') {
      return next(new AppError('Verified status must be a boolean', 400));
    }
    
    const result = await documentVaultService.verifyDocument(
      documentId,
      req.user._id,
      verified,
      notes
    );
    
    res.status(200).json({
      status: 'success',
      message: `Document ${verified ? 'verified' : 'rejected'} successfully`,
      data: result
    });
  })
);

/**
 * @swagger
 * /api/documents/admin/{documentId}/download:
 *   get:
 *     summary: Download any document (Admin only)
 *     tags: [Documents, Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: documentId
 *         required: true
 *         schema:
 *           type: string
 *         description: Document ID
 *     responses:
 *       200:
 *         description: Document downloaded successfully
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Document not found
 */
router.get('/admin/:documentId/download',
  auth,
  requireAdmin,
  documentDownloadLimit,
  catchAsync(async (req, res, next) => {
    const { documentId } = req.params;
    
    const fileData = await documentVaultService.downloadDocument(
      documentId,
      req.user._id,
      req.user.role,
      req.ip
    );
    
    res.set({
      'Content-Type': fileData.mimeType,
      'Content-Disposition': `attachment; filename="${fileData.filename}"`,
      'Content-Length': fileData.size
    });
    
    res.send(fileData.data);
  })
);

module.exports = router;