const cheerio = require("cheerio");
const Listing = require("../../models/Listing");
const { sendAlerts } = require("../alertService");
const {
  browserPool,
  validateAndNormalizeListing,
  setupPageStealth,
  autoScroll,
  scrapingMetrics,
  isRetryableError
} = require("../scraperUtils");
const { validateAndNormalizeListingEnhanced } = require("../transformationIntegration");

// Helper function to fetch detailed listing information
const fetchListingDetails = async (browser, url) => {
  let detailPage = null;
  try {
    detailPage = await browser.newPage();
    await setupPageStealth(detailPage);

    await detailPage.goto(url, {
      waitUntil: "networkidle2",
      timeout: 30000,
    });

    // Wait for content to load
    await new Promise((r) => setTimeout(r, 2000));

    const detailHtml = await detailPage.content();
    const $ = cheerio.load(detailHtml);

    // Save HTML for debugging if needed
    // require("fs").writeFileSync("funda_detail.html", detailHtml);

    // Initialize all possible fields
    let price = "Prijs op aanvraag";
    let size = null;
    let bedrooms = null;
    let rooms = null;
    let description = null;
    let year = null;
    let interior = null;
    let propertyType = "woning";
    let energyLabel = null;
    let availableFrom = null;
    let constructionPeriod = null;
    let garden = null;
    let balcony = null;
    let parking = null;
    let heating = null;
    let isolation = null;
    let images = [];

    // Try to extract price from various selectors first
    const priceSelectors = [
      '[data-test-id="price-rent"]',
      '[data-test-id="price"]',
      ".object-header__price",
      ".price-label",
      ".fd-m-price",
      ".object-header__price-label",
      ".object-price",
      '[class*="price"]',
    ];

    for (const selector of priceSelectors) {
      const priceElement = $(selector);
      if (priceElement.length && priceElement.text().trim()) {
        price = priceElement.text().trim();
        console.log(`Found price with selector ${selector}: ${price}`);
        break;
      }
    }

    // If no price found with selectors, try regex patterns on the HTML content
    if (price === "Prijs op aanvraag") {
      const pricePatterns = [
        /€\s*[\d.,]+\s*per\s*maand/gi,
        /€\s*[\d.,]+\s*\/\s*maand/gi,
        /€\s*[\d.,]+\s*p\.m\./gi,
        /huurprijs[:\s]*€\s*[\d.,]+/gi,
      ];

      for (const pattern of pricePatterns) {
        const matches = detailHtml.match(pattern);
        if (matches && matches.length > 0) {
          price = matches[0].trim();
          console.log(`Found price with pattern ${pattern}: ${price}`);
          break;
        }
      }
    }

    // Try to extract price from JSON-LD data
    if (price === "Prijs op aanvraag") {
      const jsonLdScripts = $('script[type="application/ld+json"]');
      jsonLdScripts.each((i, script) => {
        try {
          const jsonData = JSON.parse($(script).html());

          // Look for price in various JSON-LD properties
          if (jsonData.offers && jsonData.offers.price) {
            price = `€ ${jsonData.offers.price}`;
            console.log(`Found price in JSON-LD offers: ${price}`);
            return false; // break out of each loop
          }

          if (
            jsonData.priceSpecification &&
            jsonData.priceSpecification.price
          ) {
            price = `€ ${jsonData.priceSpecification.price}`;
            console.log(`Found price in JSON-LD priceSpecification: ${price}`);
            return false;
          }

          // Search for price in the JSON string
          const jsonStr = JSON.stringify(jsonData);
          const priceMatch = jsonStr.match(/"price":\s*"?([^"\,}]+)"?/i);
          if (
            priceMatch &&
            priceMatch[1] &&
            !isNaN(parseFloat(priceMatch[1]))
          ) {
            price = `€ ${priceMatch[1]}`;
            console.log(`Found price in JSON-LD data: ${price}`);
            return false;
          }
        } catch (e) {
          // Invalid JSON, continue
        }
      });
    }

    // Extract size from various patterns and selectors
    const sizeSelectors = [
      '[data-test-id="floor-area"]',
      '[data-test-id="living-area"]',
      '.object-kenmerken-list__size',
      '.object-kenmerken-list__living-area',
      '.kenmerken-highlighted__item--wonen span',
      '.fd-o-kenmerken-list__item--wonen span',
    ];

    for (const selector of sizeSelectors) {
      const sizeElement = $(selector);
      if (sizeElement.length && sizeElement.text().trim()) {
        size = sizeElement.text().trim();
        if (!size.includes('m²')) {
          size += ' m²';
        }
        console.log(`Found size with selector ${selector}: ${size}`);
        break;
      }
    }

    if (!size) {
      const sizePatterns = [
        /(\d+)\s*m²/i,
        /(\d+)\s*vierkante\s*meter/i,
        /oppervlakte[:\s]*(\d+)\s*m²/i,
        /woonoppervlakte[:\s]*(\d+)\s*m²/i,
      ];

      for (const pattern of sizePatterns) {
        const sizeMatch = detailHtml.match(pattern);
        if (sizeMatch) {
          size = sizeMatch[1] + " m²";
          break;
        }
      }
    }

    // Extract bedrooms from various selectors
    const bedroomSelectors = [
      '[data-test-id="bedrooms"]',
      '[data-test-id="number-of-bedrooms"]',
      '.object-kenmerken-list__bedrooms',
      '.kenmerken-highlighted__item--slaapkamers span',
      '.fd-o-kenmerken-list__item--slaapkamers span',
    ];

    for (const selector of bedroomSelectors) {
      const bedroomElement = $(selector);
      if (bedroomElement.length && bedroomElement.text().trim()) {
        const bedroomText = bedroomElement.text().trim();
        const bedroomMatch = bedroomText.match(/(\d+)/);
        if (bedroomMatch) {
          bedrooms = bedroomMatch[1];
          console.log(`Found bedrooms with selector ${selector}: ${bedrooms}`);
          break;
        }
      }
    }

    if (!bedrooms) {
      const bedroomPatterns = [
        /(\d+)\s*slaapkamer/i,
        /(\d+)\s*bedroom/i,
        /slaapkamers[:\s]*(\d+)/i,
      ];

      for (const pattern of bedroomPatterns) {
        const bedroomMatch = detailHtml.match(pattern);
        if (bedroomMatch) {
          bedrooms = bedroomMatch[1];
          break;
        }
      }
    }

    // Extract total rooms
    const roomSelectors = [
      '[data-test-id="rooms"]',
      '[data-test-id="number-of-rooms"]',
      '.object-kenmerken-list__rooms',
      '.kenmerken-highlighted__item--kamers span',
      '.fd-o-kenmerken-list__item--kamers span',
    ];

    for (const selector of roomSelectors) {
      const roomElement = $(selector);
      if (roomElement.length && roomElement.text().trim()) {
        const roomText = roomElement.text().trim();
        const roomMatch = roomText.match(/(\d+)/);
        if (roomMatch) {
          rooms = roomMatch[1];
          console.log(`Found rooms with selector ${selector}: ${rooms}`);
          break;
        }
      }
    }

    if (!rooms) {
      const roomPatterns = [
        /(\d+)\s*kamer/i,
        /(\d+)\s*room/i,
        /kamers[:\s]*(\d+)/i,
      ];

      for (const pattern of roomPatterns) {
        const roomMatch = detailHtml.match(pattern);
        if (roomMatch) {
          rooms = roomMatch[1];
          break;
        }
      }
    }

    // Extract description
    const descriptionSelectors = [
      '[data-test-id="description"]',
      '[data-test-id="object-description"]',
      '.object-description__text',
      '.fd-o-description__text',
      '.object-description',
      '.object-description-body',
      '.object-description-text',
      '.fd-description',
      '.description',
      '[class*="description"]',
      '.object-header__description',
      '.object-summary',
      '.object-summary__text'
    ];

    console.log(`Attempting to extract description from ${url}`);
    for (const selector of descriptionSelectors) {
      const descElement = $(selector);
      console.log(`Trying selector ${selector}: found ${descElement.length} elements`);
      if (descElement.length && descElement.text().trim()) {
        description = descElement.text().trim()
          .replace(/\s+/g, ' ')
          .replace(/\n+/g, ' ')
          .substring(0, 1000); // Limit description length
        console.log(`✅ Found description with selector ${selector}: ${description.substring(0, 50)}...`);
        break;
      }
    }

    // If no description found with selectors, try to find it in the page content
    if (!description) {
      // Look for common Dutch description patterns
      const descriptionPatterns = [
        /beschrijving[:\s]*(.*?)(?=\n\n|\r\n\r\n|<\/)/is,
        /omschrijving[:\s]*(.*?)(?=\n\n|\r\n\r\n|<\/)/is,
        /toelichting[:\s]*(.*?)(?=\n\n|\r\n\r\n|<\/)/is
      ];

      for (const pattern of descriptionPatterns) {
        const descMatch = detailHtml.match(pattern);
        if (descMatch && descMatch[1] && descMatch[1].trim().length > 20) {
          description = descMatch[1].trim()
            .replace(/<[^>]*>/g, '') // Remove HTML tags
            .replace(/\s+/g, ' ')
            .substring(0, 1000);
          console.log(`Found description with pattern: ${description.substring(0, 50)}...`);
          break;
        }
      }
    }

    // If still no description, try to extract from meta tags
    if (!description) {
      const metaDescription = $('meta[name="description"]').attr('content');
      if (metaDescription && metaDescription.trim().length > 20) {
        description = metaDescription.trim().substring(0, 1000);
        console.log(`Found description in meta tag: ${description.substring(0, 50)}...`);
      }
    }

    // Extract construction year
    const yearSelectors = [
      '[data-test-id="construction-period"]',
      '[data-test-id="build-year"]',
      '.object-kenmerken-list__construction-period',
      '.object-kenmerken-list__build-year',
    ];

    for (const selector of yearSelectors) {
      const yearElement = $(selector);
      if (yearElement.length && yearElement.text().trim()) {
        const yearText = yearElement.text().trim();
        const yearMatch = yearText.match(/(\d{4})/);
        if (yearMatch) {
          year = yearMatch[1];
          console.log(`Found year with selector ${selector}: ${year}`);
          break;
        } else {
          // Handle period ranges like "1906-1930"
          year = yearText;
          console.log(`Found construction period: ${year}`);
          break;
        }
      }
    }

    if (!year) {
      const yearPatterns = [
        /bouwjaar[:\s]*(\d{4})/i,
        /gebouwd\s+in\s+(\d{4})/i,
        /construction\s+year[:\s]*(\d{4})/i,
      ];

      for (const pattern of yearPatterns) {
        const yearMatch = detailHtml.match(pattern);
        if (yearMatch) {
          year = yearMatch[1];
          break;
        }
      }
    }

    // Extract interior type
    const interiorSelectors = [
      '[data-test-id="interior"]',
      '[data-test-id="furnishing"]',
      '.object-kenmerken-list__interior',
      '.object-kenmerken-list__furnishing',
    ];

    for (const selector of interiorSelectors) {
      const interiorElement = $(selector);
      if (interiorElement.length && interiorElement.text().trim()) {
        interior = interiorElement.text().trim();
        console.log(`Found interior with selector ${selector}: ${interior}`);
        break;
      }
    }

    if (!interior) {
      const interiorPatterns = [
        /gestoffeerd/i,
        /gemeubileerd/i,
        /kaal/i,
        /furnished/i,
        /unfurnished/i,
        /semi-furnished/i,
      ];

      for (const pattern of interiorPatterns) {
        const interiorMatch = detailHtml.match(pattern);
        if (interiorMatch) {
          interior = interiorMatch[0];
          // Normalize interior terms
          if (/furnished/i.test(interior)) interior = "Gemeubileerd";
          if (/unfurnished/i.test(interior)) interior = "Kaal";
          if (/semi-furnished/i.test(interior)) interior = "Gestoffeerd";
          break;
        }
      }
    }

    // Extract property type (if not already determined from URL)
    const propertyTypeSelectors = [
      '[data-test-id="property-type"]',
      '[data-test-id="house-type"]',
      '.object-kenmerken-list__property-type',
    ];

    for (const selector of propertyTypeSelectors) {
      const typeElement = $(selector);
      if (typeElement.length && typeElement.text().trim()) {
        const typeText = typeElement.text().trim().toLowerCase();
        if (typeText.includes('appartement')) {
          propertyType = 'appartement';
        } else if (typeText.includes('huis') || typeText.includes('woning')) {
          propertyType = 'huis';
        } else if (typeText.includes('kamer')) {
          propertyType = 'kamer';
        } else if (typeText.includes('studio')) {
          propertyType = 'studio';
        }
        console.log(`Found property type with selector ${selector}: ${propertyType}`);
        break;
      }
    }

    // Extract energy label
    const energyLabelSelectors = [
      '[data-test-id="energy-label"]',
      '.object-kenmerken-list__energy-label',
    ];

    for (const selector of energyLabelSelectors) {
      const labelElement = $(selector);
      if (labelElement.length && labelElement.text().trim()) {
        energyLabel = labelElement.text().trim();
        console.log(`Found energy label with selector ${selector}: ${energyLabel}`);
        break;
      }
    }

    // Extract available from date
    const availableSelectors = [
      '[data-test-id="acceptance"]',
      '[data-test-id="available-from"]',
      '.object-kenmerken-list__acceptance',
    ];

    for (const selector of availableSelectors) {
      const availableElement = $(selector);
      if (availableElement.length && availableElement.text().trim()) {
        availableFrom = availableElement.text().trim();
        console.log(`Found availability with selector ${selector}: ${availableFrom}`);
        break;
      }
    }

    // Extract garden information
    const gardenSelectors = [
      '[data-test-id="garden"]',
      '.object-kenmerken-list__garden',
    ];

    for (const selector of gardenSelectors) {
      const gardenElement = $(selector);
      if (gardenElement.length && gardenElement.text().trim()) {
        garden = gardenElement.text().trim();
        console.log(`Found garden info with selector ${selector}: ${garden}`);
        break;
      }
    }

    // Extract balcony information
    const balconySelectors = [
      '[data-test-id="balcony"]',
      '.object-kenmerken-list__balcony',
    ];

    for (const selector of balconySelectors) {
      const balconyElement = $(selector);
      if (balconyElement.length && balconyElement.text().trim()) {
        balcony = balconyElement.text().trim();
        console.log(`Found balcony info with selector ${selector}: ${balcony}`);
        break;
      }
    }

    // Extract parking information
    const parkingSelectors = [
      '[data-test-id="parking"]',
      '.object-kenmerken-list__parking',
    ];

    for (const selector of parkingSelectors) {
      const parkingElement = $(selector);
      if (parkingElement.length && parkingElement.text().trim()) {
        parking = parkingElement.text().trim();
        console.log(`Found parking info with selector ${selector}: ${parking}`);
        break;
      }
    }

    // Extract heating information
    const heatingSelectors = [
      '[data-test-id="heating"]',
      '.object-kenmerken-list__heating',
    ];

    for (const selector of heatingSelectors) {
      const heatingElement = $(selector);
      if (heatingElement.length && heatingElement.text().trim()) {
        heating = heatingElement.text().trim();
        console.log(`Found heating info with selector ${selector}: ${heating}`);
        break;
      }
    }

    // Extract isolation information
    const isolationSelectors = [
      '[data-test-id="insulation"]',
      '.object-kenmerken-list__insulation',
    ];

    for (const selector of isolationSelectors) {
      const isolationElement = $(selector);
      if (isolationElement.length && isolationElement.text().trim()) {
        isolation = isolationElement.text().trim();
        console.log(`Found isolation info with selector ${selector}: ${isolation}`);
        break;
      }
    }

    // Extract images
    console.log("Extracting images from listing...");
    const imageSelectors = [
      // Main image gallery
      '.media-viewer-overview img',
      '.object-media-fotos img',
      '.object-image-container img',
      // Thumbnail gallery
      '.media-viewer-thumbnails img',
      '.object-media-thumbnails img',
      // Other possible selectors
      '[data-test-id="object-image"] img',
      '[data-test-id="media-viewer"] img',
      '.object-header__media-viewer img',
      // Generic image selectors as fallback
      '.object-detail-images img',
      '.object-images img'
    ];

    for (const selector of imageSelectors) {
      const imageElements = $(selector);
      if (imageElements.length) {
        imageElements.each((i, img) => {
          // Try to get the highest resolution version of the image
          let imgSrc = $(img).attr('data-lazy-srcset') || 
                      $(img).attr('srcset') || 
                      $(img).attr('data-srcset') || 
                      $(img).attr('data-lazy-src') || 
                      $(img).attr('data-src') || 
                      $(img).attr('src');
          
          // If we have a srcset, extract the largest image URL
          if (imgSrc && imgSrc.includes(',')) {
            const srcsetParts = imgSrc.split(',');
            // Get the last part which usually has the highest resolution
            const lastPart = srcsetParts[srcsetParts.length - 1].trim();
            // Extract the URL part before the size descriptor
            imgSrc = lastPart.split(' ')[0];
          }

          if (imgSrc && !images.includes(imgSrc)) {
            // Make sure the URL is absolute
            if (imgSrc.startsWith('/')) {
              imgSrc = 'https://www.funda.nl' + imgSrc;
            }
            
            // Skip tiny images, icons, and logos
            if (!imgSrc.includes('icon') && !imgSrc.includes('logo')) {
              images.push(imgSrc);
            }
          }
        });
        
        if (images.length > 0) {
          console.log(`Found ${images.length} images with selector ${selector}`);
          break;
        }
      }
    }

    // If no images found with selectors, try to extract from JSON-LD data
    if (images.length === 0) {
      const jsonLdScripts = $('script[type="application/ld+json"]');
      jsonLdScripts.each((i, script) => {
        try {
          const jsonData = JSON.parse($(script).html());
          
          // Look for images in various JSON-LD properties
          if (jsonData.image) {
            if (Array.isArray(jsonData.image)) {
              jsonData.image.forEach(img => {
                if (typeof img === 'string' && !images.includes(img)) {
                  images.push(img);
                }
              });
            } else if (typeof jsonData.image === 'string' && !images.includes(jsonData.image)) {
              images.push(jsonData.image);
            }
          }
          
          // Check for images in other common properties
          if (jsonData.photo && Array.isArray(jsonData.photo)) {
            jsonData.photo.forEach(photo => {
              if (photo.contentUrl && !images.includes(photo.contentUrl)) {
                images.push(photo.contentUrl);
              }
            });
          }
        } catch (e) {
          // Invalid JSON, continue
        }
      });
      
      if (images.length > 0) {
        console.log(`Found ${images.length} images in JSON-LD data`);
      }
    }

    // Limit the number of images to prevent excessive data
    if (images.length > 10) {
      console.log(`Limiting images from ${images.length} to 10`);
      images = images.slice(0, 10);
    }

    return { 
      price, 
      size, 
      bedrooms, 
      rooms,
      description,
      year,
      interior,
      propertyType,
      energyLabel,
      availableFrom,
      garden,
      balcony,
      parking,
      heating,
      isolation,
      images
    };
  } catch (error) {
    console.log(`Error fetching details for ${url}:`, error.message);
    return { 
      price: "Prijs op aanvraag", 
      size: null, 
      bedrooms: null,
      rooms: null,
      description: null,
      year: null,
      interior: null,
      images: []
    };
  } finally {
    if (detailPage) {
      await detailPage.close();
    }
  }
};

const scrapeFunda = async (retryCount = 0, maxRetries = 3) => {
  if (retryCount === 0) {
    scrapingMetrics.recordScrapeStart();
  }

  let browser = null;
  let page = null;
  let listingsSaved = 0;
  let duplicatesSkipped = 0;
  const allListings = [];

  try {
    // Use browser pool instead of launching new browser each time
    browser = await browserPool.getBrowser();
    page = await browser.newPage();

    // Apply stealth settings
    await setupPageStealth(page);

    // Enable JavaScript
    await page.setJavaScriptEnabled(true);

    // Add cookies to make it look like a returning user
    await page.setCookie(
      {
        name: "OptanonAlertBoxClosed",
        value: new Date().toISOString(),
        domain: ".funda.nl",
        httpOnly: false,
        secure: true,
      },
      {
        name: "OptanonConsent",
        value:
          "isGpcEnabled=0&datestamp=" +
          new Date().toISOString() +
          "&version=6.26.0",
        domain: ".funda.nl",
        httpOnly: false,
        secure: true,
      },
      {
        name: "ajs_anonymous_id",
        value: "%22" + Math.random().toString(36).substring(2, 15) + "%22",
        domain: ".funda.nl",
        httpOnly: false,
        secure: true,
      }
    );

    // Navigate to the page with a realistic timeout and networkidle setting
    // Use Utrecht-specific URL with more results per page
    console.log("Navigating to Funda rental listings page for Utrecht...");
    await page.goto(
      "https://www.funda.nl/zoeken/huur?selected_area=[%22utrecht%22]&search_result=1&object_type=[%22house%22,%22apartment%22]&availability=[%22available%22,%22negotiations%22]&sort=%22date_down%22",
      {
        waitUntil: "networkidle0",
        timeout: 90000,
      }
    );

    // Add random delay to simulate human behavior (2-5 seconds)
    const randomDelay = Math.floor(Math.random() * 3000) + 2000;
    console.log(`Waiting for ${randomDelay}ms to simulate human behavior...`);
    await new Promise((r) => setTimeout(r, randomDelay));

    // Scroll down to trigger lazy loading content
    console.log("Scrolling through page to load all content...");
    await autoScroll(page);

    // Add another small delay after scrolling
    await new Promise((r) => setTimeout(r, 1000));

    // Method 1: Extract listings from JSON-LD metadata with pagination
    console.log("Extracting listings from JSON-LD metadata with pagination...");
    const listings = [];
    const maxPages = 5; // Limit to first 5 pages to avoid being blocked
    let currentPage = 1;
    
    while (currentPage <= maxPages) {
      console.log(`Scraping page ${currentPage}...`);
      
      // Navigate to specific page if not the first one
      if (currentPage > 1) {
        const pageUrl = `https://www.funda.nl/zoeken/huur?selected_area=[%22utrecht%22]&search_result=${currentPage}&object_type=[%22house%22,%22apartment%22]&availability=[%22available%22,%22negotiations%22]&sort=%22date_down%22`;
        console.log(`Navigating to page ${currentPage}: ${pageUrl}`);
        await page.goto(pageUrl, {
          waitUntil: "networkidle0",
          timeout: 90000,
        });
        
        // Add delay between pages to avoid being blocked
        const pageDelay = Math.floor(Math.random() * 3000) + 2000;
        console.log(`Waiting ${pageDelay}ms between pages...`);
        await new Promise((r) => setTimeout(r, pageDelay));
        
        // Scroll to load content
        await autoScroll(page);
        await new Promise((r) => setTimeout(r, 1000));
      }
      
      // Save HTML for inspection (only first page)
      if (currentPage === 1) {
        console.log("Saving page HTML for inspection...");
        const html = await page.content();
        require("fs").writeFileSync("funda_page.html", html);
      }
      
      const html = await page.content();
      let pageListings = 0;

      // Find the JSON-LD script containing listing data
      const jsonLdMatches = html.match(
        /\<script type="application\/ld\+json" data-hid="result-list-metadata"\>(.+?)\<\/script\>/s
      );

      if (jsonLdMatches && jsonLdMatches.length > 1) {
      try {
        const jsonLdData = JSON.parse(jsonLdMatches[1]);

        if (
          jsonLdData &&
          jsonLdData.itemListElement &&
          Array.isArray(jsonLdData.itemListElement)
        ) {
          console.log(
            `Found ${jsonLdData.itemListElement.length} total listings in JSON-LD data`
          );

          // Filter only rental listings
          const rentalListings = jsonLdData.itemListElement.filter(
            (item) => item.url && item.url.includes("/huur/")
          );

          console.log(`Filtered to ${rentalListings.length} rental listings`);

          // Process each rental listing URL to extract data
          for (const item of rentalListings) {
            const urlParts = item.url.split("/");
            const id = urlParts[urlParts.length - 1];

            // Extract property details from URL structure
            let propertyType = "woning";
            let city = "";
            let streetName = "";
            let title = "";

            // Parse URL parts: /detail/huur/[city]/[property-type]-[street-name]/[id]/
            const huurIndex = urlParts.indexOf("huur");
            if (huurIndex !== -1 && huurIndex + 1 < urlParts.length) {
              // City is the first segment after 'huur'
              if (huurIndex + 1 < urlParts.length) {
                city = urlParts[huurIndex + 1].replace(/-/g, " ");
                // Capitalize first letter of each word
                city = city.replace(/\b\w/g, (l) => l.toUpperCase());
              }

              // Property type and street name are combined in the next segment
              if (huurIndex + 2 < urlParts.length) {
                const propertyStreetSegment = urlParts[huurIndex + 2];

                // Check if it starts with a known property type
                if (propertyStreetSegment.startsWith("appartement-")) {
                  propertyType = "appartement";
                  streetName = propertyStreetSegment
                    .substring(12)
                    .replace(/-/g, " ");
                } else if (propertyStreetSegment.startsWith("huis-")) {
                  propertyType = "huis";
                  streetName = propertyStreetSegment
                    .substring(5)
                    .replace(/-/g, " ");
                } else if (propertyStreetSegment.startsWith("kamer-")) {
                  propertyType = "kamer";
                  streetName = propertyStreetSegment
                    .substring(6)
                    .replace(/-/g, " ");
                } else if (
                  propertyStreetSegment.startsWith("parkeergelegenheid-")
                ) {
                  propertyType = "parkeergelegenheid";
                  streetName = propertyStreetSegment
                    .substring(19)
                    .replace(/-/g, " ");
                } else {
                  // Default to house if no specific type found
                  propertyType = "huis";
                  streetName = propertyStreetSegment.replace(/-/g, " ");
                }

                // Capitalize street name properly
                streetName = streetName.replace(/\b\w/g, (l) =>
                  l.toUpperCase()
                );
                title = streetName;
              }
            }

            // Create a listing object with extracted data
            const listingData = {
              title: title || `${propertyType} in ${city}`,
              url: item.url,
              location: city,
              propertyType,
              price: "Prijs op aanvraag", // Will be updated if we can extract actual price
              source: "funda.nl",
              dateAdded: new Date(),
            };

            console.log(
              `Found listing: ${listingData.title} - ${listingData.location}`
            );

            // Try to fetch detailed information including price
            console.log(`Fetching details for: ${item.url}`);
            const details = await fetchListingDetails(browser, item.url);

            // Update listing with detailed information
            listingData.price = details.price;
            if (details.size) listingData.size = details.size;
            if (details.bedrooms) listingData.bedrooms = details.bedrooms;
            if (details.rooms) listingData.rooms = details.rooms;
            if (details.description) {
              listingData.description = details.description;
              console.log(`✅ Added description to listing: ${details.description.substring(0, 50)}...`);
            } else {
              console.log(`⚠️ No description found for listing: ${listingData.title}`);
            }
            if (details.year) listingData.year = details.year;
            if (details.interior) listingData.interior = details.interior;
            if (details.propertyType) listingData.propertyType = details.propertyType;
            if (details.images && details.images.length > 0) listingData.images = details.images;
            
            // Store additional details as extended properties
            const extendedDetails = {};
            if (details.energyLabel) extendedDetails.energyLabel = details.energyLabel;
            if (details.availableFrom) extendedDetails.availableFrom = details.availableFrom;
            if (details.garden) extendedDetails.garden = details.garden;
            if (details.balcony) extendedDetails.balcony = details.balcony;
            if (details.parking) extendedDetails.parking = details.parking;
            if (details.heating) extendedDetails.heating = details.heating;
            if (details.isolation) extendedDetails.isolation = details.isolation;
            
            // Add extended details if any were found
            if (Object.keys(extendedDetails).length > 0) {
              listingData.extendedDetails = JSON.stringify(extendedDetails);
            }

            listings.push(listingData);
            pageListings++;
          }
        }
      } catch (jsonError) {
        console.error("Error parsing JSON-LD data:", jsonError);
      }
      }

      // Method 2: If JSON-LD doesn't work, try HTML parsing as fallback
      if (pageListings === 0) {
        console.log("Falling back to HTML parsing...");

        // Load into cheerio for parsing
        const $ = cheerio.load(html);

        // Look for listing cards - this is the most direct way
        $('ol[data-test-id="search-results"] > li').each((index, element) => {
          try {
            // Skip elements without data-test-id (those are usually ads)
            if (!$(element).attr("data-test-id")) return;

            // Extract key elements
            const titleElement = $(element).find(
              'h2[data-test-id="street-name-house-number"]'
            );
            const priceElement = $(element).find('p[data-test-id="price"]');
            const locationElement = $(element).find(
              'p[data-test-id="postal-code-city"]'
            );
            const linkElement = $(element).find(
              'a[data-test-id="object-image-link"]'
            );

            if (linkElement.length) {
              const url = "https://www.funda.nl" + linkElement.attr("href");

              // Skip duplicates
              const isDuplicate = listings.some((listing) => listing.url === url);
              if (isDuplicate) return;

              // Extract basic details
              const title = titleElement.length ? titleElement.text().trim() : "";
              const price = priceElement.length
                ? priceElement.text().trim()
                : "Prijs op aanvraag";
              const location = locationElement.length
                ? locationElement.text().trim()
                : "";

              // Extract property type from URL
              const urlParts = url.split("/");
              const propertyIndex = urlParts.indexOf("huur") + 1;
              let propertyType = "woning";
              if (
                propertyIndex < urlParts.length &&
                ["appartement", "huis", "parkeergelegenheid", "kamer"].includes(
                  urlParts[propertyIndex]
                )
              ) {
                propertyType = urlParts[propertyIndex];
              }

            // Extract additional details if available
            let details = {};
            const detailsElement = $(element).find(
              'ul[data-test-id="kenmerken-highlightedkeypoints"]'
            );
            if (detailsElement.length) {
              detailsElement.find("li").each((i, detail) => {
                const detailText = $(detail).text().trim();
                if (detailText.includes("m²")) {
                  details.size = detailText;
                } else if (detailText.includes("slaapkamer")) {
                  details.bedrooms = detailText;
                }
              });
            }

            // Create listing object
            const listingData = {
              title: title || `${propertyType} in ${location}`,
              url,
              location,
              propertyType,
              price,
              source: "funda.nl",
              ...details,
              dateAdded: new Date(),
            };

            console.log(
              `Found listing via HTML: ${listingData.title} - ${listingData.price} - ${listingData.location}`
            );
            listings.push(listingData);
          }
        } catch (err) {
          console.error("Error processing listing element:", err);
        }
      });

      // If we still have no listings, try a more generic approach with all links
      if (listings.length === 0) {
        console.log("Trying generic link extraction approach...");
        $('a[href*="detail/huur"]').each((index, element) => {
          try {
            const url = $(element).attr("href");
            if (!url) return;

            const fullUrl = url.startsWith("http")
              ? url
              : "https://www.funda.nl" + url;

            // Skip duplicates
            const isDuplicate = listings.some(
              (listing) => listing.url === fullUrl
            );
            if (isDuplicate) return;

            const urlParts = fullUrl.split("/");
            const huurIndex = urlParts.indexOf("huur");

            // Extract property type and city
            let propertyType = "woning";
            let city = "";

            if (huurIndex !== -1) {
              if (
                huurIndex + 1 < urlParts.length &&
                ["appartement", "huis", "parkeergelegenheid", "kamer"].includes(
                  urlParts[huurIndex + 1]
                )
              ) {
                propertyType = urlParts[huurIndex + 1];
              }

              if (huurIndex + 2 < urlParts.length) {
                city = urlParts[huurIndex + 2].replace(/-/g, " ");
              }
            }

            // Try to find price nearby in the DOM
            let price = "Prijs op aanvraag";
            const parentEl = $(element).closest("li, div, article");
            if (parentEl.length) {
              const priceText = parentEl.text();
              const priceMatch = priceText.match(
                /(€\s?[\d.,]+\s?[\w\s]*\/?\s?\w+|op\s+aanvraag)/i
              );
              if (priceMatch) {
                price = priceMatch[1].trim();
              }
            }

            listings.push({
              title: `${propertyType} in ${city}`,
              url: fullUrl,
              location: city,
              propertyType,
              price,
              source: "funda.nl",
              dateAdded: new Date(),
            });
            pageListings++;
          } catch (err) {
            console.error("Error processing listing link:", err);
          }
        });
      }
      
      console.log(`Page ${currentPage}: Found ${pageListings} listings`);
      
      // If no listings found on this page, stop pagination
      if (pageListings === 0) {
        console.log(`No more listings found on page ${currentPage}, stopping pagination`);
        break;
      }
      
      currentPage++;
    }
    
    console.log(`Total listings found across all pages: ${listings.length}`);

    if (listings.length > 0) {
      // Transform and save listings to database using unified schema
      for (const rawListingData of listings) {
        // Transform the listing data using the unified schema
        const transformedData = await validateAndNormalizeListingEnhanced(rawListingData);
        
        if (!transformedData) {
          console.log(
            `Funda: Skipping invalid listing: ${
              rawListingData.title || "Unknown"
            }`
          );
          continue;
        }

        console.log(
          `Funda: Processing listing: ${transformedData.title} at ${transformedData.url}`
        );
        try {
          const newListing = new Listing(transformedData);
          await newListing.save();
          console.log(`Funda: Saved listing: ${newListing.title}`);
          listingsSaved++;
          sendAlerts(newListing);
        } catch (error) {
          if (error.code === 11000) {
            // Duplicate key error
            console.log(
              `Funda: Skipping duplicate listing: ${transformedData.title}`
            );
            duplicatesSkipped++;
          } else {
            console.error(
              `Funda: Error saving listing ${transformedData.title}:`,
              error
            );
          }
        }
      }
    } else {
      console.log("No listings found on Funda");
    }

    scrapingMetrics.recordScrapeSuccess(
      listings.length,
      listingsSaved,
      duplicatesSkipped
    );
    console.log(
      `Funda scraping completed. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
    return listings;
  } catch (error) {
    console.error(
      `Error during Funda scraping (attempt ${retryCount + 1}/${
        maxRetries + 1
      }):`,
      error
    );

    // Record failure only on first attempt
    if (retryCount === 0) {
      scrapingMetrics.recordScrapeFailure(error);
    }

    // Note: Don't close browser here since we're using a pool

    // Retry logic for transient errors
    if (retryCount < maxRetries && isRetryableError(error)) {
      console.log(
        `Retrying Funda scraping in ${(retryCount + 1) * 5} seconds...`
      );
      await new Promise((resolve) =>
        setTimeout(resolve, (retryCount + 1) * 5000)
      );
      return scrapeFunda(retryCount + 1, maxRetries);
    }

    console.log(
      `Funda scraping failed after ${
        maxRetries + 1
      } attempts. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
    return [];
  } finally {
    // Close the page to free up resources
    if (page) {
      try {
        await page.close();
      } catch (closeError) {
        console.error("Error closing Funda page:", closeError);
      }
    }
  }
};

module.exports = {
  scrapeFunda
};
