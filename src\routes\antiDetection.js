const express = require('express');
const router = express.Router();
const { auth, requireAdmin } = require('../middleware/auth');
// Initialize anti-detection system
const AntiDetectionSystem = require('../services/antiDetectionSystem');
const antiDetectionSystem = new AntiDetectionSystem();

/**
 * @swagger
 * components:
 *   schemas:
 *     AntiDetectionStats:
 *       type: object
 *       properties:
 *         activeSessions:
 *           type: number
 *           description: Number of active browser sessions
 *         totalPageViews:
 *           type: number
 *           description: Total page views across all sessions
 *         oldestSession:
 *           type: number
 *           description: Timestamp of the oldest active session
 *         profiles:
 *           type: number
 *           description: Number of available stealth profiles
 *         captchaDetections:
 *           type: number
 *           description: Total CAPTCHA detections
 *         blockingDetections:
 *           type: number
 *           description: Total blocking detections
 *         adaptationCount:
 *           type: number
 *           description: Number of adaptations made to new measures
 *     
 *     StealthProfile:
 *       type: object
 *       properties:
 *         userAgent:
 *           type: string
 *           description: Browser user agent string
 *         viewport:
 *           type: object
 *           properties:
 *             width:
 *               type: number
 *             height:
 *               type: number
 *         languages:
 *           type: array
 *           items:
 *             type: string
 *           description: Browser language preferences
 *         timezone:
 *           type: string
 *           description: Browser timezone
 *         platform:
 *           type: string
 *           description: Operating system platform
 *         hardwareConcurrency:
 *           type: number
 *           description: Number of CPU cores
 *         deviceMemory:
 *           type: number
 *           description: Device memory in GB
 */

/**
 * @swagger
 * /api/anti-detection/stats:
 *   get:
 *     summary: Get anti-detection system statistics
 *     description: Retrieve comprehensive statistics about the anti-detection system (Admin only)
 *     tags: [Anti Detection]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Anti-detection statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   $ref: '#/components/schemas/AntiDetectionStats'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 *       500:
 *         description: Internal server error
 */
router.get('/stats', auth, requireAdmin, async (req, res, next) => {
  try {
    const stats = antiDetectionSystem.getSessionStats();
    
    res.json({
      status: 'success',
      data: stats
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/anti-detection/profiles:
 *   get:
 *     summary: Get available stealth profiles
 *     description: Retrieve all available browser stealth profiles (Admin only)
 *     tags: [Anti Detection]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Stealth profiles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     profiles:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/StealthProfile'
 *                     count:
 *                       type: number
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 *       500:
 *         description: Internal server error
 */
router.get('/profiles', auth, requireAdmin, async (req, res, next) => {
  try {
    const profiles = antiDetectionSystem.stealthProfiles;
    
    res.json({
      status: 'success',
      data: {
        profiles,
        count: profiles.length
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/anti-detection/cleanup:
 *   post:
 *     summary: Cleanup all browser sessions
 *     description: Force cleanup of all active browser sessions (Admin only)
 *     tags: [Anti Detection]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Sessions cleaned up successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: All browser sessions cleaned up successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     sessionsCleanedUp:
 *                       type: number
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 *       500:
 *         description: Internal server error
 */
router.post('/cleanup', auth, requireAdmin, async (req, res, next) => {
  try {
    const sessionCount = antiDetectionSystem.sessionData.size;
    await antiDetectionSystem.cleanupAllSessions();
    
    res.json({
      status: 'success',
      message: 'All browser sessions cleaned up successfully',
      data: {
        sessionsCleanedUp: sessionCount
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/anti-detection/adapt:
 *   post:
 *     summary: Trigger adaptation to new anti-automation measures
 *     description: Manually trigger adaptation to new detection measures (Admin only)
 *     tags: [Anti Detection]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [fingerprint, behavior, rate_limit, captcha]
 *                 description: Type of detection measure to adapt to
 *               description:
 *                 type: string
 *                 description: Description of the new measure
 *               severity:
 *                 type: string
 *                 enum: [low, medium, high, critical]
 *                 default: medium
 *                 description: Severity level of the detection measure
 *     responses:
 *       200:
 *         description: Adaptation triggered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Adaptation triggered successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     adapted:
 *                       type: boolean
 *                     recommendedDelay:
 *                       type: number
 *                       description: Recommended delay in milliseconds (if applicable)
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 *       500:
 *         description: Internal server error
 */
router.post('/adapt', auth, requireAdmin, async (req, res, next) => {
  try {
    const { type, description, severity = 'medium' } = req.body;
    
    if (!['fingerprint', 'behavior', 'rate_limit', 'captcha'].includes(type)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid detection type'
      });
    }
    
    const adaptationResult = await antiDetectionSystem.adaptToChanges({
      type,
      description,
      severity,
      timestamp: new Date()
    });
    
    res.json({
      status: 'success',
      message: 'Adaptation triggered successfully',
      data: adaptationResult
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;