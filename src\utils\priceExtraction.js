/**
 * Price Extraction Utility
 * 
 * Utility functions for extracting numeric values from price strings in various European formats.
 * Handles edge cases like null values, malformed strings, and different separator conventions.
 */

/**
 * Extracts numeric price value from various European price string formats
 * 
 * @param {string|number|null|undefined} priceString - The price string to extract from
 * @returns {number} - The extracted numeric price value, or 0 if extraction fails
 * 
 * @example
 * extractNumericPrice('€ 2.850 per maand') // returns 2850
 * extractNumericPrice('€2,950') // returns 2950
 * extractNumericPrice('€ 3.500,50 per maand') // returns 3500.5
 * extractNumericPrice('Prijs op aanvraag') // returns 0
 * extractNumericPrice(null) // returns 0
 */
function extractNumericPrice(priceString) {
  // Handle null, undefined, or empty values
  if (!priceString && priceString !== 0) {
    return 0;
  }

  // If already a number, return it
  if (typeof priceString === 'number') {
    return isNaN(priceString) ? 0 : priceString;
  }

  // Convert to string for processing
  const stringValue = String(priceString).trim();
  
  // Handle empty string
  if (!stringValue) {
    return 0;
  }

  // Handle special cases (non-numeric price indicators)
  const specialCases = [
    'prijs op aanvraag',
    'op aanvraag', 
    'n.o.t.k.',
    'notk',
    'price on request',
    'poa'
  ];
  
  if (specialCases.some(special => stringValue.toLowerCase().includes(special))) {
    return 0;
  }

  // Extract price pattern - look for euro symbol followed by numbers
  // Matches patterns like: €2.850, € 2.850, €2,950, € 3.500,50, etc.
  const euroMatch = stringValue.match(/€\s*([\d.,]+)/);
  
  let numericString;
  if (euroMatch) {
    numericString = euroMatch[1];
  } else {
    // If no euro symbol, try to extract any numeric pattern
    const numericMatch = stringValue.match(/([\d.,]+)/);
    if (!numericMatch) {
      return 0;
    }
    numericString = numericMatch[1];
  }

  // Handle European number formatting
  return parseEuropeanNumber(numericString);
}

/**
 * Parses European number formats where dots can be thousands separators
 * and commas can be decimal separators
 * 
 * @param {string} numericString - The numeric string to parse
 * @returns {number} - The parsed number
 * 
 * @example
 * parseEuropeanNumber('2.850') // returns 2850 (dot as thousands separator)
 * parseEuropeanNumber('2.850,50') // returns 2850.5 (dot=thousands, comma=decimal)
 * parseEuropeanNumber('2,950') // returns 2950 (comma as thousands separator)
 * parseEuropeanNumber('2,5') // returns 2.5 (comma as decimal separator)
 */
function parseEuropeanNumber(numericString) {
  if (!numericString) {
    return 0;
  }

  // Remove any trailing characters like dashes
  numericString = numericString.replace(/[-]+$/, '');

  const hasDot = numericString.includes('.');
  const hasComma = numericString.includes(',');

  if (hasDot && hasComma) {
    // Format: 1.500,50 or 1,500.50
    // Determine which is thousands separator and which is decimal
    const dotIndex = numericString.lastIndexOf('.');
    const commaIndex = numericString.lastIndexOf(',');
    
    if (dotIndex > commaIndex) {
      // Format: 1,500.50 (comma=thousands, dot=decimal)
      numericString = numericString.replace(/,/g, '');
    } else {
      // Format: 1.500,50 (dot=thousands, comma=decimal)
      numericString = numericString.replace(/\./g, '').replace(',', '.');
    }
  } else if (hasDot) {
    // Only dots present
    const dotCount = (numericString.match(/\./g) || []).length;
    
    if (dotCount > 1) {
      // Multiple dots: treat all as thousands separators (e.g., 1.500.000)
      numericString = numericString.replace(/\./g, '');
    } else {
      // Single dot: check if it's thousands separator or decimal
      const parts = numericString.split('.');
      if (parts.length === 2 && parts[1].length === 3 && parts[0].length <= 3) {
        // Likely thousands separator: 2.850 -> 2850
        numericString = numericString.replace('.', '');
      }
      // Otherwise treat as decimal separator (e.g., 2.5)
    }
  } else if (hasComma) {
    // Only commas present
    const commaCount = (numericString.match(/,/g) || []).length;
    
    if (commaCount > 1) {
      // Multiple commas: treat all as thousands separators
      numericString = numericString.replace(/,/g, '');
    } else {
      // Single comma: check if it's thousands separator or decimal
      const parts = numericString.split(',');
      if (parts.length === 2 && parts[1].length === 3 && parts[0].length <= 3) {
        // Likely thousands separator: 1,500 -> 1500
        numericString = numericString.replace(',', '');
      } else {
        // Treat as decimal separator: 1,5 -> 1.5
        numericString = numericString.replace(',', '.');
      }
    }
  }

  const result = parseFloat(numericString);
  return isNaN(result) ? 0 : result;
}

/**
 * Validates if a price string contains a valid numeric price
 * 
 * @param {string|number|null|undefined} priceString - The price string to validate
 * @returns {boolean} - True if the price contains a valid numeric value
 * 
 * @example
 * isValidPrice('€ 2.850 per maand') // returns true
 * isValidPrice('Prijs op aanvraag') // returns false
 * isValidPrice(null) // returns false
 */
function isValidPrice(priceString) {
  const extracted = extractNumericPrice(priceString);
  return extracted > 0;
}

/**
 * Formats a numeric price value as a European-style currency string
 * 
 * @param {number} price - The numeric price to format
 * @param {boolean} includePerMonth - Whether to include "per maand" suffix
 * @returns {string} - The formatted price string
 * 
 * @example
 * formatPrice(2850) // returns '€ 2.850'
 * formatPrice(2850, true) // returns '€ 2.850 per maand'
 */
function formatPrice(price, includePerMonth = false) {
  if (typeof price !== 'number' || isNaN(price)) {
    return '€ 0';
  }

  // Handle decimal numbers by rounding to avoid formatting issues
  const roundedPrice = Math.round(price);
  const absPrice = Math.abs(roundedPrice);
  const sign = roundedPrice < 0 ? '-' : '';
  
  // Convert to string and add thousands separators
  const priceStr = absPrice.toString();
  const parts = [];
  
  // Split into groups of 3 from right to left
  for (let i = priceStr.length; i > 0; i -= 3) {
    const start = Math.max(0, i - 3);
    parts.unshift(priceStr.slice(start, i));
  }
  
  const formatted = `€ ${sign}${parts.join('.')}`;
  return includePerMonth ? `${formatted} per maand` : formatted;
}

module.exports = {
  extractNumericPrice,
  parseEuropeanNumber,
  isValidPrice,
  formatPrice
};