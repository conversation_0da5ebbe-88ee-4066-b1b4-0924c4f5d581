version: '3.8'

services:
  puppeteer-test:
    build:
      context: .
      dockerfile: Dockerfile.puppeteer-test
    container_name: zakmakelaar-puppeteer-test
    volumes:
      - ./test-results:/app/test-results
    environment:
      - NODE_ENV=test
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
    command: >
      sh -c "
        echo '🐳 Starting Puppeteer tests in Docker...' &&
        npm run test:form-automation:integration &&
        echo '🧪 Running Puppeteer browser test...' &&
        npm run test:puppeteer &&
        echo '🏠 Running real form automation test...' &&
        npm run test:real-form-automation &&
        echo '📸 Testing screenshot functionality...' &&
        npm run test:screenshots &&
        echo '✅ All tests completed!'
      "
    
  # Optional: MongoDB for full integration tests
  mongodb:
    image: mongo:6
    container_name: zakmakelaar-test-mongo
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

volumes:
  mongo_data: