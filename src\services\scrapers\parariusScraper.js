const cheerio = require("cheerio");
const Listing = require("../../models/Listing");
const { sendAlerts } = require("../alertService");
const {
  browserPool,
  validateAndNormalizeListing,
  setupPageStealth,
  getRandomDelay,
  scrapingMetrics,
  autoScroll
} = require("../scraperUtils");
const { validateAndNormalizeListingEnhanced } = require("../transformationIntegration");

// Helper function to fetch detailed listing information
const fetchListingDetails = async (browser, url) => {
  let detailPage = null;
  try {
    detailPage = await browser.newPage();
    await setupPageStealth(detailPage);

    await detailPage.goto(url, {
      waitUntil: "networkidle2",
      timeout: 30000,
    });

    // Wait for content to load
    await new Promise((r) => setTimeout(r, 2000));
    
    // Scroll to load all content
    await autoScroll(detailPage);

    const detailHtml = await detailPage.content();
    const $ = cheerio.load(detailHtml);

    // Initialize all possible fields
    let price = null;
    let size = null;
    let bedrooms = null;
    let rooms = null;
    let description = null;
    let year = null;
    let interior = null;
    let propertyType = "woning";
    let energyLabel = null;
    let availableFrom = null;
    let garden = null;
    let balcony = null;
    let parking = null;
    let heating = null;
    let isolation = null;
    let images = [];

    // Extract price
    const priceElement = $(".listing-detail-summary__price");
    if (priceElement.length) {
      price = priceElement.text().trim();
      console.log(`Found price: ${price}`);
    }
    
    // Extract basic property details from summary area (this is where Pararius actually puts the key info)
    console.log("Extracting property details from summary area...");
    
    // Try to find the summary list with key details
    const summarySelectors = [
      '.listing-detail-summary__list li',
      '.listing-detail-summary ul li',
      '.listing-summary__list li',
      '.property-summary li',
      '.listing-detail-summary .listing-detail-summary__feature'
    ];
    
    for (const summarySelector of summarySelectors) {
      const summaryItems = $(summarySelector);
      if (summaryItems.length > 0) {
        console.log(`Found ${summaryItems.length} summary items using selector: ${summarySelector}`);
        
        summaryItems.each((i, item) => {
          const text = $(item).text().trim().toLowerCase();
          console.log(`Processing summary item: "${text}"`);
          
          // Extract size (m² or m2)
          if (text.match(/\d+\s*m[²2]/)) {
            const sizeMatch = text.match(/(\d+)\s*m[²2]/);
            if (sizeMatch) {
              size = sizeMatch[1] + ' m²';
              console.log(`Found size from summary: ${size}`);
            }
          }
          
          // Extract rooms (kamers)
          if (text.includes('kamer')) {
            const roomsMatch = text.match(/(\d+)\s*kamer/);
            if (roomsMatch) {
              rooms = roomsMatch[1];
              console.log(`Found rooms from summary: ${rooms}`);
            }
          }
          
          // Extract bedrooms (slaapkamer)
          if (text.includes('slaapkamer')) {
            const bedroomsMatch = text.match(/(\d+)\s*slaapkamer/);
            if (bedroomsMatch) {
              bedrooms = bedroomsMatch[1];
              console.log(`Found bedrooms from summary: ${bedrooms}`);
            }
          }
          
          // Extract interior type
          if (text.includes('gemeubileerd') || text.includes('gestoffeerd') || text.includes('kaal')) {
            if (text.includes('gemeubileerd')) {
              interior = 'Gemeubileerd';
            } else if (text.includes('gestoffeerd')) {
              interior = 'Gestoffeerd';
            } else if (text.includes('kaal')) {
              interior = 'Kaal';
            }
            console.log(`Found interior from summary: ${interior}`);
          }
        });
        
        // If we found some data, break from trying other selectors
        if (size || rooms || bedrooms || interior) {
          break;
        }
      }
    }

    // Extract property details from specifications table - try multiple selectors
    // First try the actual Pararius structure with dl/dt/dd elements
    console.log("Extracting property details from dl/dt/dd structure...");
    
    const dlElements = $('.listing-features__list');
    if (dlElements.length > 0) {
      console.log(`Found ${dlElements.length} dl elements with listing features`);
      
      dlElements.each((i, dl) => {
        const terms = $(dl).find('dt.listing-features__term');
        terms.each((j, term) => {
          const termText = $(term).text().trim().toLowerCase();
          const description = $(term).next('dd.listing-features__description');
          const value = description.find('.listing-features__main-description').text().trim();
          
          console.log(`Processing dl feature - Term: "${termText}", Value: "${value}"`);
          
          if (value) {
            // Extract bedrooms
            if (termText.includes('aantal slaapkamers') || termText.includes('slaapkamers') || termText.includes('bedrooms')) {
              bedrooms = value;
              console.log(`Found bedrooms from dl structure: ${bedrooms}`);
            }
            // Extract rooms
            else if (termText.includes('aantal kamers') || termText.includes('kamers') || termText.includes('rooms')) {
              rooms = value;
              console.log(`Found rooms from dl structure: ${rooms}`);
            }
            // Extract bathrooms (bonus field)
            else if (termText.includes('aantal badkamers') || termText.includes('badkamers') || termText.includes('bathrooms')) {
              // We don't have a bathrooms field in our schema, but we could log it
              console.log(`Found bathrooms from dl structure: ${value}`);
            }
            // Extract floors
            else if (termText.includes('aantal woonlagen') || termText.includes('woonlagen') || termText.includes('floors')) {
              console.log(`Found floors from dl structure: ${value}`);
            }
          }
        });
      });
    }
    
    // Fallback to other selectors if dl structure didn't yield results
    const featureSelectors = [
      ".listing-features__list .listing-features__feature",
      ".listing-detail-summary__features .listing-detail-summary__feature",
      ".object-kenmerken .object-kenmerk",
      ".kenmerken .kenmerk",
      ".property-features .feature",
      ".details-table tr"
    ];
    
    let featuresFound = false;
    for (const featureSelector of featureSelectors) {
      const features = $(featureSelector);
      if (features.length > 0) {
        console.log(`Found ${features.length} features using selector: ${featureSelector}`);
        featuresFound = true;
        
        features.each((i, el) => {
          let label, value;
          
          // Try different ways to extract label and value based on the selector
          if (featureSelector.includes('listing-features')) {
            label = $(el).find(".listing-features__label").text().trim().toLowerCase();
            value = $(el).find(".listing-features__value").text().trim();
          } else if (featureSelector.includes('listing-detail-summary')) {
            label = $(el).find(".listing-detail-summary__label").text().trim().toLowerCase();
            value = $(el).find(".listing-detail-summary__value").text().trim();
          } else if (featureSelector.includes('tr')) {
            // Table row format
            const cells = $(el).find('td');
            if (cells.length >= 2) {
              label = $(cells[0]).text().trim().toLowerCase();
              value = $(cells[1]).text().trim();
            }
          } else {
            // Generic approach - try to find any label/value structure
            const labelEl = $(el).find('.label, .key, .name, dt').first();
            const valueEl = $(el).find('.value, .val, dd').first();
            if (labelEl.length && valueEl.length) {
              label = labelEl.text().trim().toLowerCase();
              value = valueEl.text().trim();
            } else {
              // Fallback: split by colon or use child elements
              const text = $(el).text().trim();
              if (text.includes(':')) {
                const parts = text.split(':');
                if (parts.length >= 2) {
                  label = parts[0].trim().toLowerCase();
                  value = parts.slice(1).join(':').trim();
                }
              }
            }
          }
          
          if (label && value) {
            console.log(`Processing feature - Label: "${label}", Value: "${value}"`);
            
            // Match different property attributes based on the label
            if (label.includes("woonoppervlakte") || label.includes("oppervlakte") || label.includes("floor area") || 
                label.includes("living area") || label.includes("m²") || label.includes("m2")) {
              size = value;
              console.log(`Found size: ${size}`);
            } 
            else if (label.includes("slaapkamers") || label.includes("bedrooms") || label.includes("slaapkamer")) {
              bedrooms = value;
              console.log(`Found bedrooms: ${bedrooms}`);
            }
            else if (label.includes("kamers") || label.includes("rooms") || label.includes("aantal kamers")) {
              rooms = value;
              console.log(`Found rooms: ${rooms}`);
            }
            else if (label.includes("bouwjaar") || label.includes("construction year") || label.includes("built in")) {
              year = value;
              console.log(`Found year: ${year}`);
            }
            else if (label.includes("interieur") || label.includes("interior") || label.includes("furnishing") || 
                     label.includes("inrichting") || label.includes("meubilering")) {
              interior = value;
              console.log(`Found interior: ${interior}`);
              
              // Normalize interior terms
              if (interior.toLowerCase().includes("furnished") || interior.toLowerCase().includes("gemeubileerd")) {
                interior = "Gemeubileerd";
              } else if (interior.toLowerCase().includes("unfurnished") || interior.toLowerCase().includes("kaal")) {
                interior = "Kaal";
              } else if (interior.toLowerCase().includes("semi-furnished") || interior.toLowerCase().includes("gestoffeerd")) {
                interior = "Gestoffeerd";
              }
            }
            else if (label.includes("beschikbaar vanaf") || label.includes("available from") || label.includes("availability")) {
              availableFrom = value;
              console.log(`Found availability: ${availableFrom}`);
            }
            else if (label.includes("energielabel") || label.includes("energy label") || label.includes("energy")) {
              energyLabel = value;
              console.log(`Found energy label: ${energyLabel}`);
            }
            else if (label.includes("tuin") || label.includes("garden")) {
              garden = value;
              console.log(`Found garden: ${garden}`);
            }
            else if (label.includes("balkon") || label.includes("balcony")) {
              balcony = value;
              console.log(`Found balcony: ${balcony}`);
            }
            else if (label.includes("parkeergelegenheid") || label.includes("parking")) {
              parking = value;
              console.log(`Found parking: ${parking}`);
            }
            else if (label.includes("verwarming") || label.includes("heating")) {
              heating = value;
              console.log(`Found heating: ${heating}`);
            }
            else if (label.includes("isolatie") || label.includes("insulation")) {
              isolation = value;
              console.log(`Found isolation: ${isolation}`);
            }
            else if (label.includes("soort") || label.includes("type") || label.includes("property type")) {
              // Determine property type
              const typeText = value.toLowerCase();
              if (typeText.includes('appartement')) {
                propertyType = 'appartement';
              } else if (typeText.includes('huis') || typeText.includes('woning')) {
                propertyType = 'huis';
              } else if (typeText.includes('kamer')) {
                propertyType = 'kamer';
              } else if (typeText.includes('studio')) {
                propertyType = 'studio';
              }
              console.log(`Found property type: ${propertyType}`);
            }
          }
        });
        
        if (featuresFound) break; // Exit the loop if we found features
      }
    }
    
    // If no features found with any selector, log this for debugging
    if (!featuresFound) {
      console.log("No property features found with any selector - page structure may have changed");
    }

    // Extract description - try multiple selectors and don't truncate
    console.log("Extracting description from Pararius listing...");
    
    const descriptionSelectors = [
      ".listing-detail-description__additional",
      ".listing-detail-description__content", 
      ".listing-detail-description",
      ".object-description",
      ".description",
      "[data-test-id='description']",
      ".listing-description",
      ".listing-detail-summary__description",
      ".property-description",
      // Try to get description from main content areas
      "main .description",
      "article .description",
      ".content .description"
    ];
    
    for (const selector of descriptionSelectors) {
      const descriptionElement = $(selector);
      if (descriptionElement.length) {
        let tempDescription = descriptionElement.text().trim()
          .replace(/\s+/g, ' ');
        
        // Clean up common prefixes
        tempDescription = tempDescription.replace(/^(Beschrijving|Description)\s*/i, '');
        
        if (tempDescription && tempDescription.length > 50) {
          description = tempDescription;
          console.log(`Found description using selector ${selector}: ${description.substring(0, 150)}...`);
          break;
        }
      }
    }
    
    // If no description found with specific selectors, try to extract from page text
    if (!description || description.length < 50) {
      console.log("Trying to extract description from page text...");
      
      // Look for text that starts with common description indicators
      const bodyText = $('body').text();
      const descriptionPatterns = [
        /Beschrijving[\s\S]{1,2000}?(?=\n\n|Indeling|Kenmerken|$)/i,
        /Description[\s\S]{1,2000}?(?=\n\n|Layout|Features|$)/i
      ];
      
      for (const pattern of descriptionPatterns) {
        const match = bodyText.match(pattern);
        if (match) {
          description = match[0].replace(/^(Beschrijving|Description)\s*/i, '').trim();
          if (description.length > 50) {
            console.log(`Found description using pattern matching: ${description.substring(0, 150)}...`);
            break;
          }
        }
      }
    }
    
    // Extract bedroom information from description if not found in summary
    if (!bedrooms && description) {
      console.log("Trying to extract bedroom count from description...");
      
      // Look for bedroom patterns in the description text
      const bedroomPatterns = [
        /(\d+)[-\s]*slaapkamer/i,
        /(\d+)[-\s]*bedroom/i,
        /(\d+)[-\s]*bed/i,
        /\b(\d+)\s*bed\b/i
      ];
      
      for (const pattern of bedroomPatterns) {
        const match = description.match(pattern);
        if (match) {
          bedrooms = match[1];
          console.log(`Found bedrooms from description: ${bedrooms}`);
          break;
        }
      }
    }
    
    // Also try to extract bedroom info from the full page text if still not found
    if (!bedrooms) {
      console.log("Trying to extract bedroom count from full page text...");
      const fullPageText = $('body').text();
      
      const bedroomPatterns = [
        /(\d+)[-\s]*slaapkamer/i,
        /(\d+)[-\s]*bedroom/i
      ];
      
      for (const pattern of bedroomPatterns) {
        const match = fullPageText.match(pattern);
        if (match) {
          bedrooms = match[1];
          console.log(`Found bedrooms from full page text: ${bedrooms}`);
          break;
        }
      }
    }

    // Extract images - try multiple selectors
    console.log("Extracting images from Pararius listing...");
    
    const imageSelectors = [
      '.carrousel__track img',
      '.listing-media__photos img',
      '.photo-gallery img',
      '.listing-photos img',
      '.object-photos img',
      '.media-gallery img',
      '.listing-detail-media img',
      '[data-test-id="listing-photos"] img'
    ];
    
    for (const selector of imageSelectors) {
      const imageElements = $(selector);
      if (imageElements.length > 0) {
        console.log(`Found ${imageElements.length} images using selector: ${selector}`);
        
        imageElements.each((i, img) => {
          // Try to get the highest resolution version of the image
          let imgSrc = $(img).attr('data-lazy-srcset') || 
                      $(img).attr('srcset') || 
                      $(img).attr('data-srcset') || 
                      $(img).attr('data-lazy-src') || 
                      $(img).attr('data-src') || 
                      $(img).attr('src');
          
          // If we have a srcset, extract the largest image URL
          if (imgSrc && imgSrc.includes(',')) {
            const srcsetParts = imgSrc.split(',');
            // Get the last part which usually has the highest resolution
            const lastPart = srcsetParts[srcsetParts.length - 1].trim();
            // Extract the URL part before the size descriptor
            imgSrc = lastPart.split(' ')[0];
          }

          if (imgSrc && !images.includes(imgSrc)) {
            // Make sure the URL is absolute
            if (imgSrc.startsWith('/')) {
              imgSrc = 'https://www.pararius.nl' + imgSrc;
            } else if (imgSrc.startsWith('//')) {
              imgSrc = 'https:' + imgSrc;
            }
            
            // Skip tiny images, icons, logos, and thumbnails
            if (!imgSrc.includes('icon') && 
                !imgSrc.includes('logo') && 
                !imgSrc.includes('thumb') &&
                !imgSrc.includes('placeholder') &&
                !imgSrc.match(/\d+x\d+/) || imgSrc.match(/[5-9]\d\dx[5-9]\d\d/)) {
              images.push(imgSrc);
              console.log(`Found image: ${imgSrc}`);
            }
          }
        });
        
        // If we found images with this selector, we can break
        if (images.length > 0) {
          break;
        }
      }
    }
    
    // If no images found in carrousel__track, try other selectors as fallback
    if (images.length === 0) {
      const imageSelectors = [
        // Main image gallery
        '.media-viewer img',
        '.listing-detail-media__slider img',
        '.listing-carousel img',
        '.listing-detail-media__image img',
        // Thumbnail gallery
        '.listing-detail-media__thumbnails img',
        '.listing-detail-media__thumbnail img',
        // Generic selectors as fallback
        '.listing-detail-media img',
        '.listing-media img',
        '.listing-image img',
        // Very generic fallback
        '.listing img[src*="media"]',
        '.listing img[data-src*="media"]'
      ];
      
      for (const selector of imageSelectors) {
        const imageElements = $(selector);
        if (imageElements.length) {
          imageElements.each((i, img) => {
            // Try to get the highest resolution version of the image
            let imgSrc = $(img).attr('data-lazy-srcset') || 
                        $(img).attr('srcset') || 
                        $(img).attr('data-srcset') || 
                        $(img).attr('data-lazy-src') || 
                        $(img).attr('data-src') || 
                        $(img).attr('src');
            
            // If we have a srcset, extract the largest image URL
            if (imgSrc && imgSrc.includes(',')) {
              const srcsetParts = imgSrc.split(',');
              // Get the last part which usually has the highest resolution
              const lastPart = srcsetParts[srcsetParts.length - 1].trim();
              // Extract the URL part before the size descriptor
              imgSrc = lastPart.split(' ')[0];
            }

            if (imgSrc && !images.includes(imgSrc)) {
              // Make sure the URL is absolute
              if (imgSrc.startsWith('/')) {
                imgSrc = 'https://www.pararius.nl' + imgSrc;
              }
              
              // Skip tiny images, icons, and logos
              if (!imgSrc.includes('icon') && !imgSrc.includes('logo')) {
                images.push(imgSrc);
              }
            }
          });
          
          if (images.length > 0) {
            console.log(`Found ${images.length} images with selector ${selector}`);
            break;
          }
        }
      }
    }

    // If no images found with selectors, try to extract from JSON-LD data
    if (images.length === 0) {
      const jsonLdScripts = $('script[type="application/ld+json"]');
      jsonLdScripts.each((i, script) => {
        try {
          const jsonData = JSON.parse($(script).html());
          
          // Look for images in various JSON-LD properties
          if (jsonData.image) {
            if (Array.isArray(jsonData.image)) {
              jsonData.image.forEach(img => {
                if (typeof img === 'string' && !images.includes(img)) {
                  images.push(img);
                }
              });
            } else if (typeof jsonData.image === 'string' && !images.includes(jsonData.image)) {
              images.push(jsonData.image);
            }
          }
          
          // Check for images in other common properties
          if (jsonData.photo && Array.isArray(jsonData.photo)) {
            jsonData.photo.forEach(photo => {
              if (photo.contentUrl && !images.includes(photo.contentUrl)) {
                images.push(photo.contentUrl);
              }
            });
          }
        } catch (e) {
          // Invalid JSON, continue
        }
      });
      
      if (images.length > 0) {
        console.log(`Found ${images.length} images in JSON-LD data`);
      }
    }

    // Limit the number of images to prevent excessive data
    if (images.length > 10) {
      console.log(`Limiting images from ${images.length} to 10`);
      images = images.slice(0, 10);
    }

    return { 
      price, 
      size, 
      bedrooms, 
      rooms,
      description,
      year,
      interior,
      propertyType,
      energyLabel,
      availableFrom,
      garden,
      balcony,
      parking,
      heating,
      isolation,
      images
    };
  } catch (error) {
    console.log(`Error fetching details for ${url}:`, error.message);
    return { 
      price: null, 
      size: null, 
      bedrooms: null,
      rooms: null,
      description: null,
      year: null,
      interior: null,
      images: []
    };
  } finally {
    if (detailPage) {
      await detailPage.close();
    }
  }
};

const scrapePararius = async () => {
  scrapingMetrics.recordScrapeStart();
  let browser = null;
  let page = null;
  let listingsSaved = 0;
  let duplicatesSkipped = 0;

  try {
    browser = await browserPool.getBrowser();
    page = await browser.newPage();

    await setupPageStealth(page);
    await page.goto("https://www.pararius.nl/huurwoningen/nederland", {
      waitUntil: "networkidle2",
      timeout: 60000,
    });

    // Add random delay
    await new Promise((resolve) =>
      setTimeout(resolve, getRandomDelay(1000, 3000))
    );

    const html = await page.content();
    const $ = cheerio.load(html);

    const listings = [];
    const listingElements = $(".search-list__item--listing");
    console.log(`Found ${listingElements.length} listing elements on the page`);
    
    // Extract basic information from the search results page
    const basicListings = [];
    listingElements.each((index, element) => {
      const titleElement = $(element).find(".listing-search-item__title a");
      const title = titleElement.text().trim();

      // Get price and clean it up
      let price = $(element).find(".listing-search-item__price").text().trim();

      // Clean up price by taking only the first line (before any newlines)
      if (price) {
        const priceLines = price.split("\n");
        price = priceLines[0].trim();
        // Remove non-breaking spaces and extra whitespace
        price = price
          .replace(/\u00A0/g, " ")
          .replace(/\s+/g, " ")
          .trim();
      }

      const location = $(element)
        .find(".listing-search-item__sub-title")
        .text()
        .trim();

      const href = titleElement.attr("href");
      const url = href ? "https://www.pararius.nl" + href : null;

      // Determine basic property type from title
      let propertyType = "woning";
      if (title.toLowerCase().includes("appartement")) {
        propertyType = "appartement";
      } else if (title.toLowerCase().includes("huis")) {
        propertyType = "huis";
      } else if (title.toLowerCase().includes("kamer")) {
        propertyType = "kamer";
      } else if (title.toLowerCase().includes("studio")) {
        propertyType = "studio";
      }

      if (title && url && location) {
        basicListings.push({
          title,
          price: price || "Prijs op aanvraag",
          location,
          url,
          propertyType,
          source: "pararius.nl",
        });
      }
    });

    console.log(`Extracted ${basicListings.length} basic listings`);

    // Fetch detailed information for each listing
    for (const basicListing of basicListings) {
      try {
        console.log(`Fetching details for: ${basicListing.url}`);
        const details = await fetchListingDetails(browser, basicListing.url);
        
        // Merge basic listing with detailed information
        const enhancedListing = {
          ...basicListing,
          // Override with details if available
          price: details.price || basicListing.price,
          propertyType: details.propertyType || basicListing.propertyType,
        };
        
        // Add additional details if available
        if (details.size) enhancedListing.size = details.size;
        if (details.bedrooms) enhancedListing.bedrooms = details.bedrooms;
        if (details.rooms) enhancedListing.rooms = details.rooms;
        if (details.description) enhancedListing.description = details.description;
        if (details.year) enhancedListing.year = details.year;
        if (details.interior) enhancedListing.interior = details.interior;
        if (details.images && details.images.length > 0) enhancedListing.images = details.images;
        
        // Store additional details as extended properties
        const extendedDetails = {};
        if (details.energyLabel) extendedDetails.energyLabel = details.energyLabel;
        if (details.availableFrom) extendedDetails.availableFrom = details.availableFrom;
        if (details.garden) extendedDetails.garden = details.garden;
        if (details.balcony) extendedDetails.balcony = details.balcony;
        if (details.parking) extendedDetails.parking = details.parking;
        if (details.heating) extendedDetails.heating = details.heating;
        if (details.isolation) extendedDetails.isolation = details.isolation;
        
        // Add extended details if any were found
        if (Object.keys(extendedDetails).length > 0) {
          enhancedListing.extendedDetails = JSON.stringify(extendedDetails);
        }
        
        // Validate and normalize the enhanced listing
        const validatedListing = validateAndNormalizeListing(enhancedListing);
        
        if (validatedListing) {
          console.log(
            `Pararius found: ${validatedListing.title} - ${validatedListing.price} - ${validatedListing.location}`
          );
          listings.push(validatedListing);
        }
        
        // Add a small delay between requests to avoid overloading the server
        await new Promise(resolve => setTimeout(resolve, getRandomDelay(1000, 2000)));
      } catch (error) {
        console.error(`Error processing listing ${basicListing.url}:`, error.message);
      }
    }

    console.log(`Found ${listings.length} listings from Pararius.`);

    // Process listings with unified schema transformation
    for (const listingData of listings) {
      try {
        // Transform the listing data using the unified schema
        const transformedData = await validateAndNormalizeListingEnhanced(listingData);
        
        if (!transformedData) {
          console.log(`Skipping invalid listing: ${listingData.title || 'Unknown'}`);
          continue;
        }
        
        const newListing = new Listing(transformedData);
        await newListing.save();
        console.log(`Saved listing: ${newListing.title}`);
        listingsSaved++;
        sendAlerts(newListing);
      } catch (error) {
        if (error.code === 11000) {
          // Duplicate key error
          console.log(`Skipping duplicate listing: ${listingData.title}`);
          duplicatesSkipped++;
        } else {
          console.error(`Error saving listing ${listingData.title}:`, error);
        }
      }
    }

    scrapingMetrics.recordScrapeSuccess(
      listings.length,
      listingsSaved,
      duplicatesSkipped
    );
    return listings;
  } catch (error) {
    console.error("Error during Pararius scraping:", error);
    scrapingMetrics.recordScrapeFailure(error);
    return [];
  } finally {
    // Close the page to free up resources
    if (page) {
      try {
        await page.close();
      } catch (closeError) {
        console.error("Error closing Pararius page:", closeError);
      }
    }
    console.log(
      `Pararius scraping completed. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
  }
};

module.exports = {
  scrapePararius
};
