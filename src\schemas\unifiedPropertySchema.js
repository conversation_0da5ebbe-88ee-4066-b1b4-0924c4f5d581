/**
 * Unified Property Schema Definition
 * 
 * This schema defines the standardized structure for all scraped property data,
 * ensuring consistency across different sources (Funda, Huurwoningen, Pararius)
 * while maintaining compatibility with the frontend expectations.
 */

const Joi = require('joi');

// Core unified property schema with frontend compatibility
const UnifiedPropertySchema = Joi.object({
  // Basic Information (Frontend Required)
  _id: Joi.string().optional(), // MongoDB ObjectId as string
  id: Joi.string().optional(),  // Alias for _id for compatibility
  title: Joi.string().required().min(1).max(500),
  description: Joi.string().allow('', null).max(5000),
  
  // Source Information
  source: Joi.string().required().valid('funda.nl', 'huurwoningen.nl', 'pararius.nl'),
  url: Joi.string().uri().required(),
  dateAdded: Joi.string().isoDate().default(() => new Date().toISOString()),
  
  // Location Data (Frontend Compatible - supports both string and object)
  location: Joi.alternatives().try(
    // Simple string format (legacy compatibility)
    Joi.string().min(1).max(200),
    // Structured object format
    Joi.object({
      _unified: Joi.object({
        address: Joi.object({
          street: Joi.string().allow('', null),
          houseNumber: Joi.string().allow('', null),
          postalCode: Joi.string().allow('', null),
          city: Joi.string().allow('', null),
          province: Joi.string().allow('', null),
          country: Joi.string().default('Netherlands')
        }),
        coordinates: Joi.object({
          lat: Joi.number().min(-90).max(90),
          lng: Joi.number().min(-180).max(180)
        }).allow(null)
      }),
      _legacy: Joi.string().allow('', null), // Simple location string for backward compatibility
      toString: Joi.function().optional() // Dynamic getter function
    })
  ).required(),
  
  // Property Classification (Frontend Compatible)
  propertyType: Joi.string().valid('apartment', 'appartement', 'house', 'huis', 'studio', 'room', 'kamer', 'woning').default('woning'),
  
  // Physical Attributes (Frontend Compatible Format)
  size: Joi.string().allow('', null).pattern(/^\d+[\+]?(\s*m²?)?$/), // Formatted size string (e.g., "85 m²", "85+", "85")
  area: Joi.number().positive().min(10).max(1000).allow(null), // Numeric area in square meters (reasonable bounds)
  
  // Room information (Frontend expects string or number)
  rooms: Joi.alternatives().try(
    Joi.string().pattern(/^\d+[\+]?$/), // Allow "3" or "3+"
    Joi.number().positive().max(20) // Reasonable upper bound
  ).allow(null),
  bedrooms: Joi.alternatives().try(
    Joi.string().pattern(/^\d+[\+]?$/), // Allow "2" or "2+"
    Joi.number().positive().max(15) // Reasonable upper bound
  ).allow(null),
  bathrooms: Joi.alternatives().try(
    Joi.string().pattern(/^\d+[\+]?$/), // Allow "1" or "1+"
    Joi.number().positive().max(10) // Reasonable upper bound
  ).default("1"),
  
  // Build year (Frontend expects string)
  year: Joi.string().allow('', null).pattern(/^\d{4}$/).custom((value, helpers) => {
    if (value && value !== '') {
      const year = parseInt(value);
      const currentYear = new Date().getFullYear();
      if (year < 1800 || year > currentYear + 5) {
        return helpers.message('Build year must be between 1800 and ' + (currentYear + 5));
      }
    }
    return value;
  }),
  
  // Financial Information (Frontend Compatible)
  price: Joi.alternatives().try(
    Joi.string().min(1).max(100), // Formatted price string (reasonable length)
    Joi.number().positive().max(50000) // Numeric price (reasonable upper bound for monthly rent)
  ).required(),
  
  // Features and Amenities (Frontend Compatible)
  interior: Joi.string().valid('Kaal', 'Gestoffeerd', 'Gemeubileerd', 'kaal', 'gestoffeerd', 'gemeubileerd', 'furnished', 'unfurnished', 'semi-furnished').allow('', null),
  furnished: Joi.boolean().default(false),
  pets: Joi.boolean().default(false),
  smoking: Joi.boolean().default(false),
  garden: Joi.boolean().default(false),
  balcony: Joi.boolean().default(false),
  parking: Joi.boolean().default(false),
  energyLabel: Joi.string().valid('A+++', 'A++', 'A+', 'A', 'B', 'C', 'D', 'E', 'F', 'G').allow('', null),
  
  // Media (Frontend Compatible)
  images: Joi.array().items(Joi.string().uri()).default([]),
  
  // Additional Frontend Fields
  isActive: Joi.boolean().default(true),
  features: Joi.array().items(Joi.string().max(50)).max(20).default([]), // Reasonable limits
  deposit: Joi.number().positive().max(100000).allow(null), // Reasonable upper bound
  utilities: Joi.number().positive().max(1000).allow(null), // Reasonable upper bound
  dateAvailable: Joi.string().isoDate().allow('', null),
  
  // Contact Information (Frontend Compatible)
  contactInfo: Joi.object({
    name: Joi.string().allow('', null),
    phone: Joi.string().allow('', null),
    email: Joi.string().email().allow('', null)
  }).default({}),
  
  // Internal Processing Data (Hidden from Frontend)
  _internal: Joi.object({
    sourceMetadata: Joi.object({
      website: Joi.string(),
      externalId: Joi.string().allow('', null),
      scrapedAt: Joi.date().default(() => new Date()),
      lastUpdated: Joi.date().default(() => new Date()),
      version: Joi.number().default(1)
    }),
    rawData: Joi.object({
      original: Joi.object().unknown(true),
      processed: Joi.object().unknown(true),
      metadata: Joi.object().unknown(true)
    }),
    dataQuality: Joi.object({
      completeness: Joi.number().min(0).max(100).default(0),
      accuracy: Joi.number().min(0).max(100).default(0),
      lastValidated: Joi.date().allow(null),
      validationErrors: Joi.array().items(Joi.string()).default([])
    })
  }).default({})
});

// Validation options
const ValidationOptions = {
  abortEarly: false, // Return all validation errors
  allowUnknown: false, // Don't allow unknown fields
  stripUnknown: true, // Remove unknown fields
  convert: true, // Convert types when possible
  presence: 'optional' // Fields are optional unless marked required
};

// Strict validation options for critical operations
const StrictValidationOptions = {
  ...ValidationOptions,
  allowUnknown: false,
  presence: 'required' // All fields must be present
};

/**
 * Validate a property object against the unified schema
 * @param {Object} propertyData - The property data to validate
 * @param {Object} options - Validation options
 * @returns {Object} Validation result with value and error
 */
function validateProperty(propertyData, options = ValidationOptions) {
  return UnifiedPropertySchema.validate(propertyData, options);
}

/**
 * Validate a property object strictly (all required fields must be present)
 * @param {Object} propertyData - The property data to validate
 * @returns {Object} Validation result with value and error
 */
function validatePropertyStrict(propertyData) {
  return UnifiedPropertySchema.validate(propertyData, StrictValidationOptions);
}

/**
 * Get the schema definition for external use
 * @returns {Object} Joi schema object
 */
function getSchema() {
  return UnifiedPropertySchema;
}

/**
 * Get validation options
 * @param {boolean} strict - Whether to use strict validation
 * @returns {Object} Validation options
 */
function getValidationOptions(strict = false) {
  return strict ? StrictValidationOptions : ValidationOptions;
}

/**
 * Create a minimal valid property object with required fields
 * @param {Object} basicData - Basic property data
 * @returns {Object} Minimal valid property object
 */
function createMinimalProperty(basicData = {}) {
  return {
    title: basicData.title || 'Untitled Property',
    source: basicData.source || 'funda.nl',
    url: basicData.url || 'https://example.com',
    location: basicData.location || 'Unknown Location',
    price: basicData.price || 'Prijs op aanvraag',
    propertyType: basicData.propertyType || 'woning',
    dateAdded: new Date().toISOString(),
    ...basicData
  };
}

module.exports = {
  UnifiedPropertySchema,
  ValidationOptions,
  StrictValidationOptions,
  validateProperty,
  validatePropertyStrict,
  getSchema,
  getValidationOptions,
  createMinimalProperty
};