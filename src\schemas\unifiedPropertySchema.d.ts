/**
 * TypeScript definitions for Unified Property Schema
 * 
 * These type definitions provide better IDE support and documentation
 * for the unified property schema system.
 */

import { Schema } from 'joi';

// Core unified property interface (Frontend-Compatible)
export interface UnifiedProperty {
  // Basic Information (Frontend Required)
  _id?: string; // MongoDB ObjectId as string
  id?: string;  // Alias for _id for compatibility
  title: string;
  description?: string;
  
  // Source Information
  source: 'funda.nl' | 'huurwoningen.nl' | 'pararius.nl';
  url: string;
  dateAdded?: string; // ISO date string (frontend expects string)
  
  // Location Data (Frontend Compatible)
  location: string | LocationObject;
  
  // Property Classification (Frontend Compatible)
  propertyType?: 'apartment' | 'appartement' | 'house' | 'huis' | 'studio' | 'room' | 'kamer' | 'woning';
  
  // Physical Attributes (Frontend Compatible Format)
  size?: string;        // Formatted size string (e.g., "85 m²") - frontend expects string
  area?: number;        // Numeric area in square meters - frontend fallback
  
  // Room information (Frontend expects string or number)
  rooms?: string | number;     // Total rooms - frontend uses both formats
  bedrooms?: string | number;  // Bedrooms - frontend uses both formats  
  bathrooms?: string | number; // Bathrooms - frontend uses both formats
  
  // Build year
  year?: string;        // Build year as string - frontend expects string
  
  // Financial Information (Frontend Compatible)
  price: string | number; // Frontend expects both formats, has formatPrice utility
  
  // Features and Amenities (Frontend Compatible)
  interior?: InteriorType;    // Dutch terms
  furnished?: boolean;  // Frontend fallback boolean
  pets?: boolean;       // Frontend filter field
  smoking?: boolean;    // Frontend filter field
  garden?: boolean;     // Frontend filter field
  balcony?: boolean;    // Frontend filter field
  parking?: boolean;    // Frontend filter field
  energyLabel?: EnergyLabel; // Frontend expects string
  
  // Media (Frontend Compatible)
  images?: string[];    // Array of image URLs - frontend expects simple string array
  
  // Additional Frontend Fields
  isActive?: boolean;   // Frontend uses this field
  features?: string[];  // Frontend expects array of feature strings
  deposit?: number;     // Frontend expects numeric deposit
  utilities?: number;   // Frontend expects numeric utilities cost
  dateAvailable?: string; // Frontend expects date string
  
  // Contact Information (Frontend Compatible)
  contactInfo?: ContactInfo;
  
  // Internal Processing Data (Hidden from Frontend)
  _internal?: InternalData;
}

export interface LocationObject {
  _unified?: {
    address?: {
      street?: string;
      houseNumber?: string;
      postalCode?: string;
      city?: string;
      province?: string;
      country?: string;
    };
    coordinates?: {
      lat?: number;
      lng?: number;
    };
  };
  _legacy?: string; // Simple location string for backward compatibility
  toString?: () => string; // Dynamic getter function
}

export interface ContactInfo {
  name?: string;
  phone?: string;
  email?: string;
}

export interface InternalData {
  sourceMetadata?: {
    website?: string;
    externalId?: string;
    scrapedAt?: Date;
    lastUpdated?: Date;
    version?: number;
  };
  rawData?: {
    original?: any;
    processed?: any;
    metadata?: any;
  };
  dataQuality?: {
    completeness?: number;
    accuracy?: number;
    lastValidated?: Date;
    validationErrors?: string[];
  };
}

// Type definitions for enums
export type InteriorType = 
  | 'Kaal' | 'Gestoffeerd' | 'Gemeubileerd'  // Dutch
  | 'kaal' | 'gestoffeerd' | 'gemeubileerd'  // Dutch lowercase
  | 'furnished' | 'unfurnished' | 'semi-furnished'; // English

export type EnergyLabel = 
  | 'A+++' | 'A++' | 'A+' | 'A' | 'B' | 'C' | 'D' | 'E' | 'F' | 'G';

export type PropertyType = 
  | 'apartment' | 'appartement' | 'house' | 'huis' 
  | 'studio' | 'room' | 'kamer' | 'woning';

export type SourceWebsite = 
  | 'funda.nl' | 'huurwoningen.nl' | 'pararius.nl';

// Validation result interface
export interface ValidationResult {
  value?: UnifiedProperty;
  error?: {
    details: Array<{
      message: string;
      path: string[];
      type: string;
      context: any;
    }>;
  };
}

// Validation options interface
export interface ValidationOptions {
  abortEarly?: boolean;
  allowUnknown?: boolean;
  stripUnknown?: boolean;
  convert?: boolean;
  presence?: 'optional' | 'required';
}

// Function signatures
export declare function validateProperty(
  propertyData: any, 
  options?: ValidationOptions
): ValidationResult;

export declare function validatePropertyStrict(
  propertyData: any
): ValidationResult;

export declare function createMinimalProperty(
  basicData?: Partial<UnifiedProperty>
): UnifiedProperty;

export declare function getSchema(): Schema;

export declare function getValidationOptions(
  strict?: boolean
): ValidationOptions;

// Export the Joi schema
export declare const UnifiedPropertySchema: Schema;
export declare const ValidationOptions: ValidationOptions;
export declare const StrictValidationOptions: ValidationOptions;