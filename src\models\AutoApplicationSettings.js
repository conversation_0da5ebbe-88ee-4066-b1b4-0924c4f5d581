const mongoose = require("mongoose");
const encryptionService = require('../services/encryptionService');

const autoApplicationSettingsSchema = new mongoose.Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true,
    unique: true
  },
  
  // Auto-application enabled/disabled state
  enabled: { 
    type: Boolean, 
    default: false
  },
  
  // Application settings and preferences
  settings: {
    maxApplicationsPerDay: { 
      type: Number, 
      default: 5,
      min: 1,
      max: 20
    },
    applicationTemplate: { 
      type: String, 
      enum: ['professional', 'casual', 'student', 'expat'],
      default: 'professional'
    },
    autoSubmit: { 
      type: Boolean, 
      default: true 
    },
    requireManualReview: { 
      type: Boolean, 
      default: false 
    },
    notificationPreferences: {
      immediate: { type: Boolean, default: true },
      daily: { type: Boolean, default: true },
      weekly: { type: Boolean, default: false }
    },
    language: {
      type: String,
      enum: ['dutch', 'english'],
      default: 'english'
    }
  },
  
  // Property matching criteria
  criteria: {
    maxPrice: { type: Number, required: true },
    minRooms: { type: Number, default: 1 },
    maxRooms: { type: Number, default: 10 },
    propertyTypes: [{
      type: String,
      enum: ['apartment', 'house', 'studio', 'room']
    }],
    locations: [{ type: String }],
    excludeKeywords: [{ type: String }],
    includeKeywords: [{ type: String }],
    minSize: { type: Number },
    maxSize: { type: Number },
    furnished: { type: Boolean },
    petsAllowed: { type: Boolean },
    smokingAllowed: { type: Boolean }
  },
  
  // Personal information for applications
  personalInfo: {
    fullName: { type: String, required: true },
    email: { type: String, required: true },
    phone: { type: String, required: true },
    dateOfBirth: { type: Date, required: true },
    nationality: { type: String, required: true },
    occupation: { type: String, required: true },
    employer: { type: String, required: true },
    monthlyIncome: { type: Number, required: true },
    moveInDate: { type: Date, required: true },
    leaseDuration: { 
      type: Number, 
      required: true,
      min: 6,
      max: 60
    }, // in months
    numberOfOccupants: { 
      type: Number, 
      default: 1,
      min: 1,
      max: 10
    },
    hasGuarantor: { type: Boolean, default: false },
    guarantorInfo: {
      name: { type: String },
      email: { type: String },
      phone: { type: String },
      relationship: { type: String },
      monthlyIncome: { type: Number }
    },
    emergencyContact: {
      name: { type: String, required: true },
      phone: { type: String, required: true },
      email: { type: String },
      relationship: { type: String, required: true }
    }
  },
  
  // Required documents tracking
  documents: [{
    type: { 
      type: String, 
      enum: ['income_proof', 'employment_contract', 'bank_statement', 'id_document', 'rental_reference'],
      required: true
    },
    filename: { type: String, required: true },
    documentId: { type: mongoose.Schema.Types.ObjectId, ref: 'Document' },
    required: { type: Boolean, default: true },
    uploaded: { type: Boolean, default: false },
    uploadedAt: { type: Date },
    verified: { type: Boolean, default: false },
    verifiedAt: { type: Date }
  }],
  
  // Application statistics and tracking
  statistics: {
    totalApplications: { type: Number, default: 0 },
    successfulApplications: { type: Number, default: 0 },
    rejectedApplications: { type: Number, default: 0 },
    pendingApplications: { type: Number, default: 0 },
    lastApplicationDate: { type: Date },
    averageResponseTime: { type: Number }, // in hours
    successRate: { type: Number, default: 0 }, // percentage
    dailyApplicationCount: { type: Number, default: 0 },
    lastDailyReset: { type: Date, default: Date.now }
  },
  
  // System status and health
  status: {
    lastProcessed: { type: Date },
    isActive: { type: Boolean, default: false },
    pausedReason: { type: String },
    pausedUntil: { type: Date },
    errorCount: { type: Number, default: 0 },
    lastError: {
      message: { type: String },
      timestamp: { type: Date },
      code: { type: String }
    }
  },
  
  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes for performance optimization
// Note: userId already has unique: true in schema definition, so no need for explicit index
autoApplicationSettingsSchema.index({ enabled: 1 });
autoApplicationSettingsSchema.index({ 'status.isActive': 1 });
autoApplicationSettingsSchema.index({ 'statistics.lastApplicationDate': -1 });
autoApplicationSettingsSchema.index({ 'status.pausedUntil': 1 }, { sparse: true });
autoApplicationSettingsSchema.index({ createdAt: -1 });

// Virtual fields
autoApplicationSettingsSchema.virtual('isProfileComplete').get(function() {
  const info = this.personalInfo;
  const requiredFields = ['fullName', 'email', 'phone', 'dateOfBirth', 'nationality', 'occupation', 'employer', 'monthlyIncome', 'moveInDate', 'leaseDuration'];
  return requiredFields.every(field => info[field]);
});

autoApplicationSettingsSchema.virtual('documentsComplete').get(function() {
  const requiredDocs = this.documents.filter(doc => doc.required);
  return requiredDocs.length > 0 && requiredDocs.every(doc => doc.uploaded && doc.verified);
});

autoApplicationSettingsSchema.virtual('canAutoApply').get(function() {
  return this.enabled && 
         this.isProfileComplete && 
         this.documentsComplete && 
         !this.status.pausedUntil &&
         this.statistics.dailyApplicationCount < this.settings.maxApplicationsPerDay;
});

autoApplicationSettingsSchema.virtual('dailyApplicationsRemaining').get(function() {
  return Math.max(0, this.settings.maxApplicationsPerDay - this.statistics.dailyApplicationCount);
});

// Pre-save middleware
autoApplicationSettingsSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Encrypt sensitive personal information before saving
  if (this.isModified('personalInfo') && this.personalInfo) {
    try {
      this.personalInfo = encryptionService.encryptPersonalInfo(this.personalInfo, this.userId.toString());
    } catch (error) {
      console.error('Failed to encrypt personal info:', error);
      // Continue without encryption in case of error (log this in production)
    }
  }
  
  // Reset daily count if it's a new day
  const now = new Date();
  const lastReset = this.statistics.lastDailyReset;
  if (!lastReset || now.toDateString() !== lastReset.toDateString()) {
    this.statistics.dailyApplicationCount = 0;
    this.statistics.lastDailyReset = now;
  }
  
  // Calculate success rate
  if (this.statistics.totalApplications > 0) {
    this.statistics.successRate = Math.round(
      (this.statistics.successfulApplications / this.statistics.totalApplications) * 100
    );
  }
  
  // Update active status based on enabled state and conditions
  this.status.isActive = this.enabled && this.canAutoApply;
  
  next();
});

// Post-find middleware to decrypt sensitive data
autoApplicationSettingsSchema.post(['find', 'findOne', 'findOneAndUpdate'], function(docs) {
  if (!docs) return;
  
  const documents = Array.isArray(docs) ? docs : [docs];
  
  documents.forEach(doc => {
    if (doc && doc.personalInfo && doc.userId) {
      try {
        doc.personalInfo = encryptionService.decryptPersonalInfo(doc.personalInfo, doc.userId.toString());
      } catch (error) {
        console.error('Failed to decrypt personal info:', error);
        // Keep encrypted data if decryption fails
      }
    }
  });
});

// Static methods
autoApplicationSettingsSchema.statics.findActiveUsers = function() {
  return this.find({
    enabled: true,
    'status.isActive': true,
    $or: [
      { 'status.pausedUntil': { $exists: false } },
      { 'status.pausedUntil': { $lt: new Date() } }
    ]
  });
};

autoApplicationSettingsSchema.statics.findByUserId = function(userId) {
  return this.findOne({ userId });
};

autoApplicationSettingsSchema.statics.getUsersNeedingDocuments = function() {
  return this.find({
    enabled: true,
    'documents': {
      $elemMatch: {
        required: true,
        $or: [
          { uploaded: false },
          { verified: false }
        ]
      }
    }
  }).populate('userId', 'email profile.firstName profile.lastName');
};

// Instance methods
autoApplicationSettingsSchema.methods.incrementApplicationCount = function() {
  this.statistics.totalApplications += 1;
  this.statistics.dailyApplicationCount += 1;
  this.statistics.lastApplicationDate = new Date();
  return this.save();
};

autoApplicationSettingsSchema.methods.recordSuccess = function() {
  this.statistics.successfulApplications += 1;
  return this.save();
};

autoApplicationSettingsSchema.methods.recordRejection = function() {
  this.statistics.rejectedApplications += 1;
  return this.save();
};

autoApplicationSettingsSchema.methods.pauseAutoApplication = function(reason, duration = null) {
  this.status.pausedReason = reason;
  if (duration) {
    this.status.pausedUntil = new Date(Date.now() + duration);
  }
  this.status.isActive = false;
  return this.save();
};

autoApplicationSettingsSchema.methods.resumeAutoApplication = function() {
  this.status.pausedReason = undefined;
  this.status.pausedUntil = undefined;
  this.status.isActive = this.enabled && this.canAutoApply;
  return this.save();
};

autoApplicationSettingsSchema.methods.recordError = function(error) {
  this.status.errorCount += 1;
  this.status.lastError = {
    message: error.message,
    timestamp: new Date(),
    code: error.code || 'UNKNOWN'
  };
  
  // Auto-pause if too many errors
  if (this.status.errorCount >= 5) {
    this.pauseAutoApplication('Too many errors', 60 * 60 * 1000); // 1 hour
  }
  
  return this.save();
};

autoApplicationSettingsSchema.methods.resetErrorCount = function() {
  this.status.errorCount = 0;
  this.status.lastError = undefined;
  return this.save();
};

autoApplicationSettingsSchema.methods.updateDocument = function(type, documentData) {
  const docIndex = this.documents.findIndex(doc => doc.type === type);
  if (docIndex >= 0) {
    this.documents[docIndex] = { ...this.documents[docIndex], ...documentData };
  } else {
    this.documents.push({ type, ...documentData });
  }
  return this.save();
};

autoApplicationSettingsSchema.methods.matchesCriteria = function(listing) {
  const criteria = this.criteria;
  
  // Price check
  if (listing.price > criteria.maxPrice) return false;
  
  // Rooms check
  if (listing.rooms < criteria.minRooms || listing.rooms > criteria.maxRooms) return false;
  
  // Property type check
  if (criteria.propertyTypes.length > 0 && !criteria.propertyTypes.includes(listing.propertyType)) {
    return false;
  }
  
  // Location check
  if (criteria.locations.length > 0) {
    const matchesLocation = criteria.locations.some(location => 
      listing.location.toLowerCase().includes(location.toLowerCase())
    );
    if (!matchesLocation) return false;
  }
  
  // Size check
  if (criteria.minSize && listing.size < criteria.minSize) return false;
  if (criteria.maxSize && listing.size > criteria.maxSize) return false;
  
  // Exclude keywords check
  if (criteria.excludeKeywords.length > 0) {
    const hasExcludedKeyword = criteria.excludeKeywords.some(keyword =>
      listing.title.toLowerCase().includes(keyword.toLowerCase()) ||
      (listing.description && listing.description.toLowerCase().includes(keyword.toLowerCase()))
    );
    if (hasExcludedKeyword) return false;
  }
  
  // Include keywords check
  if (criteria.includeKeywords.length > 0) {
    const hasIncludedKeyword = criteria.includeKeywords.some(keyword =>
      listing.title.toLowerCase().includes(keyword.toLowerCase()) ||
      (listing.description && listing.description.toLowerCase().includes(keyword.toLowerCase()))
    );
    if (!hasIncludedKeyword) return false;
  }
  
  return true;
};

/**
 * Get decrypted personal info (for internal use)
 */
autoApplicationSettingsSchema.methods.getDecryptedPersonalInfo = function() {
  if (!this.personalInfo || !this.userId) {
    return this.personalInfo;
  }
  
  try {
    return encryptionService.decryptPersonalInfo(this.personalInfo, this.userId.toString());
  } catch (error) {
    console.error('Failed to decrypt personal info:', error);
    return this.personalInfo; // Return encrypted data if decryption fails
  }
};

/**
 * Update personal info with encryption
 */
autoApplicationSettingsSchema.methods.updatePersonalInfo = function(newPersonalInfo) {
  if (!newPersonalInfo || !this.userId) {
    this.personalInfo = newPersonalInfo;
    return this.save();
  }
  
  try {
    this.personalInfo = encryptionService.encryptPersonalInfo(newPersonalInfo, this.userId.toString());
    return this.save();
  } catch (error) {
    console.error('Failed to encrypt personal info during update:', error);
    throw new Error('Failed to securely update personal information');
  }
};

/**
 * Sanitize data for API response (remove sensitive fields)
 */
autoApplicationSettingsSchema.methods.toSafeObject = function() {
  const obj = this.toObject();
  
  // Remove or mask sensitive fields
  if (obj.personalInfo) {
    const decrypted = this.getDecryptedPersonalInfo();
    obj.personalInfo = {
      fullName: decrypted.fullName ? '***' : undefined,
      email: decrypted.email ? decrypted.email.replace(/(.{2}).*(@.*)/, '$1***$2') : undefined,
      phone: decrypted.phone ? decrypted.phone.replace(/(.{3}).*(.{2})/, '$1***$2') : undefined,
      hasGuarantor: decrypted.hasGuarantor,
      numberOfOccupants: decrypted.numberOfOccupants,
      moveInDate: decrypted.moveInDate,
      leaseDuration: decrypted.leaseDuration
      // Exclude sensitive fields like income, employer, etc.
    };
  }
  
  return obj;
};

// Ensure virtual fields are serialized
autoApplicationSettingsSchema.set('toJSON', { virtuals: true });
autoApplicationSettingsSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model("AutoApplicationSettings", autoApplicationSettingsSchema);