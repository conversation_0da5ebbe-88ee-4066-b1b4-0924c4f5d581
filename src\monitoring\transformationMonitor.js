/**
 * Transformation Pipeline Monitoring
 * 
 * This module provides comprehensive monitoring for the schema transformation pipeline,
 * including performance metrics tracking, data quality monitoring, and alerting.
 */

const fs = require('fs').promises;
const path = require('path');
const { loggers } = require('../services/logger');
const { SchemaError, ErrorTypes } = require('../utils/schemaErrors');

/**
 * Transformation Monitor Class
 * Handles monitoring and metrics collection for the transformation pipeline
 */
class TransformationMonitor {
  /**
   * Create a new TransformationMonitor
   */
  constructor() {
    this.metricsFile = path.join(__dirname, '../../logs/transformation-metrics.log');
    this.alertsFile = path.join(__dirname, '../../logs/transformation-alerts.log');
    this.metrics = {
      totalTransformations: 0,
      successfulTransformations: 0,
      failedTransformations: 0,
      successRate: '0%',
      averageTransformationTime: 0,
      averageTransformationTimeFormatted: '0ms',
      totalPropertiesTransformed: 0,
      errorsByType: {},
      dataQualityScores: {
        completeness: {
          sum: 0,
          count: 0,
          average: 0
        },
        accuracy: {
          sum: 0,
          count: 0,
          average: 0
        }
      },
      memoryUsage: {
        average: 0,
        peak: 0,
        samples: 0
      },
      lastTransformationTime: null,
      transformationTimes: []
    };
    
    this.alertThresholds = {
      successRate: 80, // Alert if below 80%
      avgTransformationTime: 500, // Alert if above 500ms
      completeness: 70, // Alert if below 70%
      accuracy: 70, // Alert if below 70%
      consecutiveFailures: 3,
      memoryUsage: 100 * 1024 * 1024 // 100MB
    };
    
    this.consecutiveFailures = 0;
    this.initialized = false;
  }

  /**
   * Initialize the monitor
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) return;
    
    try {
      // Ensure logs directory exists
      const logsDir = path.join(__dirname, '../../logs');
      await fs.mkdir(logsDir, { recursive: true });
      
      // Try to load existing metrics
      try {
        const metricsData = await fs.readFile(this.metricsFile, 'utf8');
        const savedMetrics = JSON.parse(metricsData);
        this.metrics = { ...this.metrics, ...savedMetrics };
      } catch (error) {
        // No existing metrics or invalid format, use defaults
        loggers.app.info('No existing transformation metrics found, starting fresh');
      }
      
      this.initialized = true;
      loggers.app.info('Transformation monitor initialized');
    } catch (error) {
      loggers.app.error('Failed to initialize transformation monitor', { error: error.message });
      throw error;
    }
  }

  /**
   * Record the start of a transformation operation
   * @param {string} source - Source identifier
   * @param {Object} options - Transformation options
   * @returns {Object} Transformation context for tracking
   */
  startTransformation(source, options = {}) {
    const context = {
      id: `transform_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
      source,
      options,
      startTime: Date.now(),
      startMemory: process.memoryUsage().heapUsed
    };
    
    loggers.app.debug('Starting transformation', {
      transformationId: context.id,
      source,
      options
    });
    
    return context;
  }

  /**
   * Record the completion of a transformation operation
   * @param {Object} context - Transformation context from startTransformation
   * @param {Object} result - Transformation result
   * @param {Object} dataQuality - Data quality metrics
   * @returns {Promise<void>}
   */
  async endTransformation(context, result, dataQuality = null) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    const endTime = Date.now();
    const duration = endTime - context.startTime;
    const endMemory = process.memoryUsage().heapUsed;
    const memoryUsed = endMemory - context.startMemory;
    
    const success = result && !result.error;
    
    // Update metrics
    this.metrics.totalTransformations++;
    
    if (success) {
      this.metrics.successfulTransformations++;
      this.consecutiveFailures = 0;
    } else {
      this.metrics.failedTransformations++;
      this.consecutiveFailures++;
      
      // Track error types
      if (result && result.error) {
        const errorType = result.error.type || 'UNKNOWN_ERROR';
        this.metrics.errorsByType[errorType] = (this.metrics.errorsByType[errorType] || 0) + 1;
      }
    }
    
    // Calculate success rate
    this.metrics.successRate = `${Math.round((this.metrics.successfulTransformations / this.metrics.totalTransformations) * 100)}%`;
    
    // Update transformation times
    this.metrics.transformationTimes.push(duration);
    if (this.metrics.transformationTimes.length > 100) {
      this.metrics.transformationTimes.shift(); // Keep only the last 100 times
    }
    
    // Calculate average transformation time
    const sum = this.metrics.transformationTimes.reduce((a, b) => a + b, 0);
    this.metrics.averageTransformationTime = Math.round(sum / this.metrics.transformationTimes.length);
    this.metrics.averageTransformationTimeFormatted = `${this.metrics.averageTransformationTime}ms`;
    
    // Update memory usage metrics
    this.metrics.memoryUsage.samples++;
    this.metrics.memoryUsage.average = 
      (this.metrics.memoryUsage.average * (this.metrics.memoryUsage.samples - 1) + memoryUsed) / 
      this.metrics.memoryUsage.samples;
    
    if (memoryUsed > this.metrics.memoryUsage.peak) {
      this.metrics.memoryUsage.peak = memoryUsed;
    }
    
    // Update data quality metrics if provided
    if (dataQuality) {
      if (typeof dataQuality.completeness === 'number') {
        this.metrics.dataQualityScores.completeness.sum += dataQuality.completeness;
        this.metrics.dataQualityScores.completeness.count++;
        this.metrics.dataQualityScores.completeness.average = 
          Math.round(this.metrics.dataQualityScores.completeness.sum / this.metrics.dataQualityScores.completeness.count);
      }
      
      if (typeof dataQuality.accuracy === 'number') {
        this.metrics.dataQualityScores.accuracy.sum += dataQuality.accuracy;
        this.metrics.dataQualityScores.accuracy.count++;
        this.metrics.dataQualityScores.accuracy.average = 
          Math.round(this.metrics.dataQualityScores.accuracy.sum / this.metrics.dataQualityScores.accuracy.count);
      }
    }
    
    // Update last transformation time
    this.metrics.lastTransformationTime = new Date().toISOString();
    
    // Log the transformation completion
    loggers.app.debug('Transformation completed', {
      transformationId: context.id,
      source: context.source,
      duration: `${duration}ms`,
      success,
      memoryUsed: `${Math.round(memoryUsed / 1024 / 1024)}MB`
    });
    
    // Check for alerts
    const alerts = this.checkAlerts();
    if (alerts.length > 0) {
      await this.handleAlerts(alerts);
    }
    
    // Save metrics to file
    await this.saveMetrics();
    
    // Return performance data
    return {
      duration,
      memoryUsed,
      success
    };
  }

  /**
   * Record a transformation error
   * @param {Object} context - Transformation context from startTransformation
   * @param {Error|SchemaError} error - Error that occurred
   * @returns {Promise<void>}
   */
  async recordError(context, error) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    const endTime = Date.now();
    const duration = endTime - context.startTime;
    
    // Convert to SchemaError if it's not already
    const schemaError = error instanceof SchemaError ? error : new SchemaError(
      ErrorTypes.TRANSFORMATION_ERROR,
      error.message,
      { 
        source: context.source,
        originalError: error
      }
    );
    
    // Log the error
    loggers.app.error('Transformation error', {
      transformationId: context.id,
      source: context.source,
      duration: `${duration}ms`,
      error: schemaError.toJSON()
    });
    
    // Update metrics
    this.metrics.totalTransformations++;
    this.metrics.failedTransformations++;
    this.consecutiveFailures++;
    
    // Track error types
    const errorType = schemaError.type || 'UNKNOWN_ERROR';
    this.metrics.errorsByType[errorType] = (this.metrics.errorsByType[errorType] || 0) + 1;
    
    // Calculate success rate
    this.metrics.successRate = `${Math.round((this.metrics.successfulTransformations / this.metrics.totalTransformations) * 100)}%`;
    
    // Update last transformation time
    this.metrics.lastTransformationTime = new Date().toISOString();
    
    // Check for alerts
    const alerts = this.checkAlerts();
    if (alerts.length > 0) {
      await this.handleAlerts(alerts);
    }
    
    // Save metrics to file
    await this.saveMetrics();
    
    return {
      duration,
      error: schemaError
    };
  }

  /**
   * Record batch transformation results
   * @param {Object} context - Transformation context from startTransformation
   * @param {Object} batchResult - Batch transformation result
   * @returns {Promise<void>}
   */
  async recordBatchResults(context, batchResult) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    const endTime = Date.now();
    const duration = endTime - context.startTime;
    const endMemory = process.memoryUsage().heapUsed;
    const memoryUsed = endMemory - context.startMemory;
    
    // Update metrics
    this.metrics.totalTransformations += batchResult.totalProcessed || 0;
    this.metrics.successfulTransformations += batchResult.successCount || 0;
    this.metrics.failedTransformations += batchResult.errorCount || 0;
    this.metrics.totalPropertiesTransformed += batchResult.successCount || 0;
    
    // Calculate success rate
    this.metrics.successRate = `${Math.round((this.metrics.successfulTransformations / this.metrics.totalTransformations) * 100)}%`;
    
    // Update memory usage metrics
    this.metrics.memoryUsage.samples++;
    this.metrics.memoryUsage.average = 
      (this.metrics.memoryUsage.average * (this.metrics.memoryUsage.samples - 1) + memoryUsed) / 
      this.metrics.memoryUsage.samples;
    
    if (memoryUsed > this.metrics.memoryUsage.peak) {
      this.metrics.memoryUsage.peak = memoryUsed;
    }
    
    // Track error types
    if (batchResult.errors && batchResult.errors.length > 0) {
      for (const error of batchResult.errors) {
        const errorType = error.error?.type || 'UNKNOWN_ERROR';
        this.metrics.errorsByType[errorType] = (this.metrics.errorsByType[errorType] || 0) + 1;
      }
    }
    
    // Update last transformation time
    this.metrics.lastTransformationTime = new Date().toISOString();
    
    // Log the batch completion
    loggers.app.info('Batch transformation completed', {
      transformationId: context.id,
      source: context.source,
      duration: `${duration}ms`,
      totalProcessed: batchResult.totalProcessed,
      successCount: batchResult.successCount,
      errorCount: batchResult.errorCount,
      memoryUsed: `${Math.round(memoryUsed / 1024 / 1024)}MB`
    });
    
    // Check for alerts
    const alerts = this.checkAlerts();
    if (alerts.length > 0) {
      await this.handleAlerts(alerts);
    }
    
    // Save metrics to file
    await this.saveMetrics();
    
    return {
      duration,
      memoryUsed,
      batchResult
    };
  }

  /**
   * Update data quality metrics
   * @param {Object} dataQuality - Data quality metrics
   * @returns {Promise<void>}
   */
  async updateDataQuality(dataQuality) {
    if (!this.initialized) {
      await this.initialize();
    }
    
    if (typeof dataQuality.completeness === 'number') {
      this.metrics.dataQualityScores.completeness.sum += dataQuality.completeness;
      this.metrics.dataQualityScores.completeness.count++;
      this.metrics.dataQualityScores.completeness.average = 
        Math.round(this.metrics.dataQualityScores.completeness.sum / this.metrics.dataQualityScores.completeness.count);
    }
    
    if (typeof dataQuality.accuracy === 'number') {
      this.metrics.dataQualityScores.accuracy.sum += dataQuality.accuracy;
      this.metrics.dataQualityScores.accuracy.count++;
      this.metrics.dataQualityScores.accuracy.average = 
        Math.round(this.metrics.dataQualityScores.accuracy.sum / this.metrics.dataQualityScores.accuracy.count);
    }
    
    // Check for data quality alerts
    const alerts = this.checkDataQualityAlerts();
    if (alerts.length > 0) {
      await this.handleAlerts(alerts);
    }
    
    // Save metrics to file
    await this.saveMetrics();
  }

  /**
   * Check for alerts based on current metrics
   * @returns {Array<Object>} Array of alerts
   */
  checkAlerts() {
    const alerts = [];
    const successRate = parseFloat(this.metrics.successRate);
    
    // Success rate alert
    if (successRate < this.alertThresholds.successRate) {
      alerts.push({
        level: successRate < 50 ? 'critical' : 'warning',
        type: 'success_rate',
        message: `Transformation success rate (${successRate}%) is below threshold (${this.alertThresholds.successRate}%)`,
        value: successRate,
        threshold: this.alertThresholds.successRate,
        timestamp: new Date().toISOString()
      });
    }
    
    // Performance alert
    if (this.metrics.averageTransformationTime > this.alertThresholds.avgTransformationTime) {
      alerts.push({
        level: 'warning',
        type: 'performance',
        message: `Average transformation time (${this.metrics.averageTransformationTime}ms) exceeds threshold (${this.alertThresholds.avgTransformationTime}ms)`,
        value: this.metrics.averageTransformationTime,
        threshold: this.alertThresholds.avgTransformationTime,
        timestamp: new Date().toISOString()
      });
    }
    
    // Consecutive failures alert
    if (this.consecutiveFailures >= this.alertThresholds.consecutiveFailures) {
      alerts.push({
        level: 'critical',
        type: 'consecutive_failures',
        message: `${this.consecutiveFailures} consecutive transformation failures detected`,
        value: this.consecutiveFailures,
        threshold: this.alertThresholds.consecutiveFailures,
        timestamp: new Date().toISOString()
      });
    }
    
    // Memory usage alert
    if (this.metrics.memoryUsage.peak > this.alertThresholds.memoryUsage) {
      alerts.push({
        level: 'warning',
        type: 'memory_usage',
        message: `Peak memory usage (${Math.round(this.metrics.memoryUsage.peak / 1024 / 1024)}MB) exceeds threshold (${Math.round(this.alertThresholds.memoryUsage / 1024 / 1024)}MB)`,
        value: this.metrics.memoryUsage.peak,
        threshold: this.alertThresholds.memoryUsage,
        timestamp: new Date().toISOString()
      });
    }
    
    return alerts;
  }

  /**
   * Check for data quality alerts
   * @returns {Array<Object>} Array of alerts
   */
  checkDataQualityAlerts() {
    const alerts = [];
    
    // Completeness alert
    if (this.metrics.dataQualityScores.completeness.average < this.alertThresholds.completeness) {
      alerts.push({
        level: 'warning',
        type: 'data_quality_completeness',
        message: `Average data completeness (${this.metrics.dataQualityScores.completeness.average}%) is below threshold (${this.alertThresholds.completeness}%)`,
        value: this.metrics.dataQualityScores.completeness.average,
        threshold: this.alertThresholds.completeness,
        timestamp: new Date().toISOString()
      });
    }
    
    // Accuracy alert
    if (this.metrics.dataQualityScores.accuracy.average < this.alertThresholds.accuracy) {
      alerts.push({
        level: 'warning',
        type: 'data_quality_accuracy',
        message: `Average data accuracy (${this.metrics.dataQualityScores.accuracy.average}%) is below threshold (${this.alertThresholds.accuracy}%)`,
        value: this.metrics.dataQualityScores.accuracy.average,
        threshold: this.alertThresholds.accuracy,
        timestamp: new Date().toISOString()
      });
    }
    
    return alerts;
  }

  /**
   * Handle alerts
   * @param {Array<Object>} alerts - Array of alerts
   * @returns {Promise<void>}
   */
  async handleAlerts(alerts) {
    if (alerts.length === 0) return;
    
    // Log alerts
    for (const alert of alerts) {
      if (alert.level === 'critical') {
        loggers.app.error(alert.message, { alert });
      } else {
        loggers.app.warn(alert.message, { alert });
      }
    }
    
    // Save alerts to file
    try {
      const alertLog = {
        timestamp: new Date().toISOString(),
        alerts,
        count: alerts.length,
        criticalCount: alerts.filter(a => a.level === 'critical').length
      };
      
      const logLine = JSON.stringify(alertLog) + '\n';
      await fs.appendFile(this.alertsFile, logLine);
    } catch (error) {
      loggers.app.error('Error writing to alerts log', { error: error.message });
    }
  }

  /**
   * Save metrics to file
   * @returns {Promise<void>}
   */
  async saveMetrics() {
    try {
      await fs.writeFile(
        this.metricsFile,
        JSON.stringify(this.metrics, null, 2)
      );
    } catch (error) {
      loggers.app.error('Error saving transformation metrics', { error: error.message });
    }
  }

  /**
   * Get current transformation metrics
   * @returns {Object} Current metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      formattedMetrics: {
        totalTransformations: this.metrics.totalTransformations.toLocaleString(),
        successfulTransformations: this.metrics.successfulTransformations.toLocaleString(),
        failedTransformations: this.metrics.failedTransformations.toLocaleString(),
        successRate: this.metrics.successRate,
        averageTransformationTime: this.metrics.averageTransformationTimeFormatted,
        totalPropertiesTransformed: this.metrics.totalPropertiesTransformed.toLocaleString(),
        dataQualityCompleteness: `${this.metrics.dataQualityScores.completeness.average}%`,
        dataQualityAccuracy: `${this.metrics.dataQualityScores.accuracy.average}%`,
        memoryUsageAverage: `${Math.round(this.metrics.memoryUsage.average / 1024 / 1024)}MB`,
        memoryUsagePeak: `${Math.round(this.metrics.memoryUsage.peak / 1024 / 1024)}MB`,
        lastTransformationTime: this.metrics.lastTransformationTime
      }
    };
  }

  /**
   * Get recent alerts
   * @param {number} count - Number of recent alerts to retrieve
   * @returns {Promise<Array<Object>>} Recent alerts
   */
  async getRecentAlerts(count = 10) {
    try {
      const content = await fs.readFile(this.alertsFile, 'utf8');
      const lines = content.trim().split('\n');
      
      return lines
        .slice(-count)
        .map(line => JSON.parse(line))
        .reverse();
    } catch (error) {
      if (error.code === 'ENOENT') {
        return [];
      }
      loggers.app.error('Error reading alerts file', { error: error.message });
      throw error;
    }
  }

  /**
   * Generate a performance report
   * @returns {Object} Performance report
   */
  generatePerformanceReport() {
    const metrics = this.getMetrics();
    
    return {
      timestamp: new Date().toISOString(),
      summary: {
        totalTransformations: metrics.totalTransformations,
        successRate: metrics.successRate,
        averageTransformationTime: metrics.averageTransformationTimeFormatted,
        dataQualityCompleteness: `${metrics.dataQualityScores.completeness.average}%`,
        dataQualityAccuracy: `${metrics.dataQualityScores.accuracy.average}%`
      },
      performance: {
        averageTransformationTime: metrics.averageTransformationTime,
        memoryUsageAverage: Math.round(metrics.memoryUsage.average / 1024 / 1024),
        memoryUsagePeak: Math.round(metrics.memoryUsage.peak / 1024 / 1024)
      },
      dataQuality: {
        completeness: metrics.dataQualityScores.completeness.average,
        accuracy: metrics.dataQualityScores.accuracy.average,
        sampleSize: metrics.dataQualityScores.completeness.count
      },
      errors: {
        totalErrors: metrics.failedTransformations,
        errorRate: `${Math.round((metrics.failedTransformations / metrics.totalTransformations) * 100)}%`,
        errorsByType: Object.entries(metrics.errorsByType).map(([type, count]) => ({
          type,
          count,
          percentage: `${Math.round((count / metrics.failedTransformations) * 100)}%`
        }))
      },
      recommendations: this.generateRecommendations(metrics)
    };
  }

  /**
   * Generate recommendations based on metrics
   * @param {Object} metrics - Current metrics
   * @returns {Array<Object>} Recommendations
   */
  generateRecommendations(metrics) {
    const recommendations = [];
    const successRate = parseFloat(metrics.successRate);
    
    if (successRate < 80) {
      recommendations.push({
        priority: 'high',
        category: 'reliability',
        message: 'Success rate is below optimal. Review error logs and improve error handling.'
      });
    }
    
    if (metrics.averageTransformationTime > 200) {
      recommendations.push({
        priority: 'medium',
        category: 'performance',
        message: 'Average transformation time is high. Consider optimizing transformation functions.'
      });
    }
    
    if (metrics.dataQualityScores.completeness.average < 80) {
      recommendations.push({
        priority: 'high',
        category: 'data_quality',
        message: 'Data completeness is below optimal. Review field mappings and default values.'
      });
    }
    
    if (metrics.dataQualityScores.accuracy.average < 80) {
      recommendations.push({
        priority: 'medium',
        category: 'data_quality',
        message: 'Data accuracy is below optimal. Review validation rules and transformation functions.'
      });
    }
    
    if (metrics.memoryUsage.peak > 50 * 1024 * 1024) { // 50MB
      recommendations.push({
        priority: 'low',
        category: 'resources',
        message: 'Memory usage is high. Consider optimizing memory usage in transformation functions.'
      });
    }
    
    return recommendations;
  }
}

// Create singleton instance
const transformationMonitor = new TransformationMonitor();

module.exports = {
  transformationMonitor,
  TransformationMonitor
};