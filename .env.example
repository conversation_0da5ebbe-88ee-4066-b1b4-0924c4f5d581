# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGO_URI=mongodb://localhost:27017/zakmakelaar

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here-make-it-long-and-random
JWT_EXPIRES_IN=7d

# SendGrid Configuration (for email notifications)
SENDGRID_API_KEY=your-sendgrid-api-key-here
SENDGRID_FROM_EMAIL=<EMAIL>

# Twilio Configuration (for WhatsApp notifications)
TWILIO_ACCOUNT_SID=your-twilio-account-sid-here
TWILIO_AUTH_TOKEN=your-twilio-auth-token-here
TWILIO_WHATSAPP_FROM=whatsapp:+***********

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Scraping Configuration
SCRAPING_INTERVAL_MINUTES=5
SCRAPING_TIMEOUT_MS=60000

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Redis Configuration (optional - if not set, caching will be disabled)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Cache Configuration
CACHE_DEFAULT_TTL=3600
CACHE_LISTINGS_TTL=300
CACHE_USER_TTL=1800
