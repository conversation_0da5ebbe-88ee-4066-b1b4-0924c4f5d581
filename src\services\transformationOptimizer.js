/**
 * Transformation Optimizer Module
 * 
 * This module provides performance optimizations for the unified schema transformation pipeline.
 * It implements caching for frequently used transformation rules and mappings,
 * concurrent processing support, and other performance enhancements.
 * 
 * Requirements:
 * - WHEN data transformation occurs THEN the system SHALL complete processing within 100ms per property record
 * - WH<PERSON> multiple scrapers run concurrently THEN the system SHALL handle schema normalization without resource contention
 * - WHEN large batches of data are processed THEN the system SHALL maintain memory usage below 500MB for transformation operations
 */

const { SchemaError, ErrorTypes } = require('../utils/schemaErrors');
const { transformationLogger } = require('../utils/transformationLogger');
const { transformationMonitor } = require('../monitoring/transformationMonitor');
const NodeCache = require('node-cache');
const os = require('os');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const path = require('path');

/**
 * Transformation Cache Class
 * Provides caching for transformation results and mapping lookups
 */
class TransformationCache {
  /**
   * Create a new TransformationCache
   * @param {Object} options - Cache options
   * @param {number} options.stdTTL - Standard TTL in seconds (default: 600)
   * @param {number} options.checkperiod - Check period in seconds (default: 60)
   * @param {number} options.maxKeys - Maximum number of keys (default: 1000)
   */
  constructor(options = {}) {
    this.cache = new NodeCache({
      stdTTL: options.stdTTL || 600, // 10 minutes
      checkperiod: options.checkperiod || 60, // 1 minute
      maxKeys: options.maxKeys || 1000
    });
    
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0
    };
  }

  /**
   * Generate a cache key for a transformation
   * @param {string} source - Source identifier
   * @param {string} field - Field name
   * @param {*} value - Input value
   * @returns {string} Cache key
   */
  _generateKey(source, field, value) {
    // Create a deterministic string representation of the value
    let valueStr;
    if (value === null || value === undefined) {
      valueStr = 'null';
    } else if (typeof value === 'object') {
      try {
        // Sort object keys for deterministic JSON
        const ordered = {};
        Object.keys(value).sort().forEach(key => {
          ordered[key] = value[key];
        });
        valueStr = JSON.stringify(ordered);
      } catch (e) {
        // If JSON serialization fails, use string representation
        valueStr = String(value);
      }
    } else {
      valueStr = String(value);
    }
    
    // Limit the key size to avoid excessive memory usage
    if (valueStr.length > 100) {
      valueStr = valueStr.substring(0, 97) + '...';
    }
    
    return `${source}:${field}:${valueStr}`;
  }

  /**
   * Get a cached transformation result
   * @param {string} source - Source identifier
   * @param {string} field - Field name
   * @param {*} value - Input value
   * @returns {*} Cached result or undefined if not found
   */
  get(source, field, value) {
    const key = this._generateKey(source, field, value);
    const result = this.cache.get(key);
    
    if (result !== undefined) {
      this.stats.hits++;
      return result;
    }
    
    this.stats.misses++;
    return undefined;
  }

  /**
   * Set a cached transformation result
   * @param {string} source - Source identifier
   * @param {string} field - Field name
   * @param {*} value - Input value
   * @param {*} result - Transformation result
   * @param {number} ttl - TTL in seconds (optional)
   */
  set(source, field, value, result, ttl = undefined) {
    const key = this._generateKey(source, field, value);
    this.cache.set(key, result, ttl);
    this.stats.sets++;
  }

  /**
   * Clear the cache
   */
  clear() {
    this.cache.flushAll();
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0
    };
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getStats() {
    const cacheStats = this.cache.getStats();
    return {
      ...this.stats,
      keys: this.cache.keys().length,
      memory: cacheStats.vsize,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses || 1)
    };
  }
}

/**
 * Concurrent Transformation Manager
 * Handles concurrent processing of transformations using worker threads
 */
class ConcurrentTransformationManager {
  /**
   * Create a new ConcurrentTransformationManager
   * @param {Object} options - Manager options
   * @param {number} options.maxWorkers - Maximum number of worker threads (default: CPU cores - 1)
   * @param {number} options.minBatchSize - Minimum batch size to use workers (default: 10)
   */
  constructor(options = {}) {
    this.maxWorkers = options.maxWorkers || Math.max(1, os.cpus().length - 1);
    this.minBatchSize = options.minBatchSize || 10;
    this.workers = [];
    this.activeWorkers = 0;
    this.workerScript = path.join(__dirname, 'transformationWorker.js');
  }

  /**
   * Process a batch of items concurrently
   * @param {Array<Object>} items - Items to process
   * @param {string} source - Source identifier
   * @param {Object} options - Processing options
   * @returns {Promise<Array<Object>>} Processed items
   */
  async processBatch(items, source, options = {}) {
    // If batch is too small or we're in a worker thread, process sequentially
    if (items.length < this.minBatchSize || !isMainThread) {
      return this._processSequentially(items, source, options);
    }
    
    // Determine optimal number of workers based on batch size and CPU cores
    const optimalWorkers = Math.min(
      this.maxWorkers,
      Math.ceil(items.length / 10), // 1 worker per 10 items
      items.length // At most 1 worker per item
    );
    
    // Split items into chunks for each worker
    const chunkSize = Math.ceil(items.length / optimalWorkers);
    const chunks = [];
    
    for (let i = 0; i < items.length; i += chunkSize) {
      chunks.push(items.slice(i, i + chunkSize));
    }
    
    // Process each chunk in a worker
    const promises = chunks.map((chunk, index) => {
      return this._processInWorker(chunk, source, options, index);
    });
    
    // Wait for all workers to complete
    const results = await Promise.all(promises);
    
    // Combine results from all workers
    return results.flat();
  }

  /**
   * Process items sequentially
   * @param {Array<Object>} items - Items to process
   * @param {string} source - Source identifier
   * @param {Object} options - Processing options
   * @returns {Promise<Array<Object>>} Processed items
   * @private
   */
  async _processSequentially(items, source, options) {
    const results = [];
    
    for (const item of items) {
      try {
        const result = await options.transformFunc(item, source, options);
        results.push(result);
      } catch (error) {
        if (options.continueOnError !== false) {
          results.push({
            error: error instanceof SchemaError ? error : new SchemaError(
              ErrorTypes.TRANSFORMATION_ERROR,
              error.message,
              { source, originalError: error }
            )
          });
        } else {
          throw error;
        }
      }
    }
    
    return results;
  }

  /**
   * Process items in a worker thread
   * @param {Array<Object>} items - Items to process
   * @param {string} source - Source identifier
   * @param {Object} options - Processing options
   * @param {number} workerId - Worker identifier
   * @returns {Promise<Array<Object>>} Processed items
   * @private
   */
  _processInWorker(items, source, options, workerId) {
    return new Promise((resolve, reject) => {
      const worker = new Worker(this.workerScript, {
        workerData: {
          items,
          source,
          options: {
            ...options,
            // Remove non-serializable options
            transformFunc: undefined
          },
          workerId
        }
      });
      
      this.activeWorkers++;
      
      worker.on('message', (result) => {
        resolve(result);
      });
      
      worker.on('error', (error) => {
        reject(error);
      });
      
      worker.on('exit', (code) => {
        this.activeWorkers--;
        
        if (code !== 0) {
          reject(new Error(`Worker stopped with exit code ${code}`));
        }
      });
    });
  }

  /**
   * Get manager statistics
   * @returns {Object} Manager statistics
   */
  getStats() {
    return {
      maxWorkers: this.maxWorkers,
      activeWorkers: this.activeWorkers,
      cpuCores: os.cpus().length
    };
  }
}

/**
 * Enhanced Schema Transformer with performance optimizations
 * Extends the base SchemaTransformer with caching and concurrent processing
 */
class OptimizedSchemaTransformer {
  /**
   * Create a new OptimizedSchemaTransformer
   * @param {SchemaTransformer} baseTransformer - Base schema transformer
   * @param {Object} options - Optimization options
   */
  constructor(baseTransformer, options = {}) {
    this.baseTransformer = baseTransformer;
    this.cache = new TransformationCache(options.cache);
    this.concurrentManager = new ConcurrentTransformationManager(options.concurrent);
    
    // Bind methods to maintain context
    this.transform = this.transform.bind(this);
    this.batchTransform = this.batchTransform.bind(this);
  }

  /**
   * Transform raw scraper data to unified schema with caching
   * @param {Object} rawData - Raw data from scraper
   * @param {string} source - Source identifier
   * @param {Object} options - Transformation options
   * @returns {Promise<Object>} Transformed property data
   */
  async transform(rawData, source, options = {}) {
    const {
      useCache = true,
      ...transformOptions
    } = options;
    
    // Skip cache for special cases
    if (!useCache || !rawData || !source) {
      return this.baseTransformer.transform(rawData, source, transformOptions);
    }
    
    // Generate a cache key based on the raw data and source
    const cacheKey = this._generateCacheKey(rawData, source);
    
    // Try to get from cache
    const cachedResult = this.cache.get('transform', cacheKey, null);
    if (cachedResult !== undefined) {
      transformationLogger.logCacheHit('transform', source);
      return JSON.parse(JSON.stringify(cachedResult)); // Deep clone to avoid reference issues
    }
    
    // Transform the data
    const result = await this.baseTransformer.transform(rawData, source, transformOptions);
    
    // Cache the result
    if (result && !result._internal?.processingMetadata?.errors?.length) {
      this.cache.set('transform', cacheKey, null, result);
      transformationLogger.logCacheStore('transform', source);
    }
    
    return result;
  }

  /**
   * Batch transform multiple raw data items with concurrent processing
   * @param {Array<Object>} rawDataItems - Array of raw data items
   * @param {string} source - Source identifier
   * @param {Object} options - Transformation options
   * @returns {Promise<Object>} Batch transformation result
   */
  async batchTransform(rawDataItems, source, options = {}) {
    const {
      useConcurrent = true,
      useCache = true,
      ...transformOptions
    } = options;
    
    // Start monitoring and logging for batch operation
    const context = transformationLogger.startTransformation(source, {
      ...transformOptions,
      batch: true,
      batchSize: rawDataItems.length,
      concurrent: useConcurrent,
      cached: useCache
    });
    
    if (!Array.isArray(rawDataItems)) {
      const error = new SchemaError(
        ErrorTypes.TRANSFORMATION_ERROR,
        'Raw data items must be an array',
        { source }
      );
      
      await transformationLogger.logError(context, error);
      throw error;
    }
    
    try {
      let results;
      
      if (useConcurrent && rawDataItems.length >= this.concurrentManager.minBatchSize) {
        // Process concurrently
        results = await this.concurrentManager.processBatch(
          rawDataItems,
          source,
          {
            ...transformOptions,
            useCache,
            transformFunc: this.transform
          }
        );
      } else {
        // Process sequentially with caching
        results = [];
        
        for (const rawData of rawDataItems) {
          try {
            const transformed = await this.transform(rawData, source, {
              ...transformOptions,
              useCache,
              throwOnError: false
            });
            results.push(transformed);
          } catch (error) {
            if (transformOptions.continueOnError !== false) {
              results.push(this.baseTransformer._createErrorResult(
                error instanceof SchemaError ? error : new SchemaError(
                  ErrorTypes.TRANSFORMATION_ERROR,
                  error.message,
                  { source, originalError: error }
                ),
                rawData
              ));
            } else {
              throw error;
            }
          }
        }
      }
      
      const errors = results.filter(r => r._internal?.processingMetadata?.errors?.length > 0);
      
      const batchResult = {
        results,
        errors: errors.map(r => ({
          rawData: r._internal?.rawData?.original,
          error: r._internal?.processingMetadata?.errors[0]
        })),
        success: errors.length === 0,
        totalProcessed: rawDataItems.length,
        successCount: results.length - errors.length,
        errorCount: errors.length
      };
      
      // Log batch results
      await transformationLogger.logBatchResults(context, batchResult);
      
      return batchResult;
    } catch (error) {
      // Log error
      await transformationLogger.logError(context, error);
      throw error;
    }
  }

  /**
   * Generate a cache key for raw data
   * @param {Object} rawData - Raw data
   * @param {string} source - Source identifier
   * @returns {string} Cache key
   * @private
   */
  _generateCacheKey(rawData, source) {
    // Use a subset of fields that uniquely identify a property
    const keyFields = {
      url: rawData.url,
      title: rawData.title,
      price: rawData.price,
      location: rawData.location
    };
    
    return JSON.stringify(keyFields);
  }

  /**
   * Clear the transformation cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Get optimization statistics
   * @returns {Object} Optimization statistics
   */
  getStats() {
    return {
      cache: this.cache.getStats(),
      concurrent: this.concurrentManager.getStats()
    };
  }
}

module.exports = {
  TransformationCache,
  ConcurrentTransformationManager,
  OptimizedSchemaTransformer
};