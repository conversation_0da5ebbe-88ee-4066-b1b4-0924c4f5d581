/**
 * Database Optimizer Module
 * 
 * This module provides optimizations for database queries and storage
 * related to the unified property schema. It implements indexing strategies,
 * query optimization, and efficient storage patterns.
 */

const mongoose = require('mongoose');
const { loggers } = require('./logger');

/**
 * Database Optimizer Class
 * Provides optimizations for database operations with unified schema
 */
class DatabaseOptimizer {
  /**
   * Create indexes for efficient querying of unified schema data
   * @param {mongoose.Model} model - Mongoose model to optimize
   * @returns {Promise<void>}
   */
  static async createOptimalIndexes(model) {
    try {
      // Get model name for logging
      const modelName = model.modelName;
      loggers.app.info(`Creating optimal indexes for ${modelName} model`);
      
      // Create compound indexes for common query patterns
      await model.collection.createIndexes([
        // Index for location-based queries (most common query pattern)
        {
          key: {
            'location._unified.address.city': 1,
            'location._unified.address.province': 1,
            'price': 1
          },
          name: 'location_price_idx'
        },
        
        // Index for property type and price range queries
        {
          key: {
            'propertyType': 1,
            'price': 1
          },
          name: 'property_type_price_idx'
        },
        
        // Index for feature-based filtering
        {
          key: {
            'furnished': 1,
            'bedrooms': 1,
            'rooms': 1
          },
          name: 'features_idx'
        },
        
        // Index for source and data quality
        {
          key: {
            'source': 1,
            '_internal.dataQuality.completeness': -1
          },
          name: 'source_quality_idx'
        },
        
        // Index for recently added properties
        {
          key: {
            'dateAdded': -1
          },
          name: 'date_added_idx'
        },
        
        // Text index for search functionality
        {
          key: {
            'title': 'text',
            'description': 'text'
          },
          name: 'text_search_idx',
          weights: {
            'title': 10,
            'description': 5
          }
        }
      ]);
      
      loggers.app.info(`Created optimal indexes for ${modelName} model`);
    } catch (error) {
      loggers.error.error(`Failed to create optimal indexes: ${error.message}`, {
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Optimize a query for unified schema data
   * @param {Object} query - MongoDB query object
   * @param {Object} options - Query options
   * @returns {Object} Optimized query
   */
  static optimizeQuery(query, options = {}) {
    const optimizedQuery = { ...query };
    const optimizedOptions = { ...options };
    
    // Optimize location queries
    if (query.location && typeof query.location === 'string') {
      // Convert simple location string queries to use the indexed fields
      optimizedQuery['$or'] = [
        { 'location._legacy': { $regex: query.location, $options: 'i' } },
        { 'location._unified.address.city': { $regex: query.location, $options: 'i' } }
      ];
      delete optimizedQuery.location;
    }
    
    // Optimize price queries
    if (query.price) {
      if (typeof query.price === 'string') {
        // Extract numeric value from price string
        const priceMatch = query.price.match(/[\d.,]+/);
        if (priceMatch) {
          const numericPrice = parseFloat(priceMatch[0].replace(',', '.'));
          if (!isNaN(numericPrice)) {
            optimizedQuery.price = numericPrice;
          }
        }
      } else if (typeof query.price === 'object') {
        // Price range query - already in optimal format
      }
    }
    
    // Optimize projection to include only needed fields
    if (!optimizedOptions.projection) {
      // Default projection that excludes large internal data
      optimizedOptions.projection = {
        '_internal.rawData': 0
      };
    }
    
    // Optimize sorting
    if (optimizedOptions.sort) {
      // Ensure we're using indexed fields for sorting when possible
      const sortField = Object.keys(optimizedOptions.sort)[0];
      
      if (sortField === 'location') {
        // Sort by city instead of location string for better performance
        optimizedOptions.sort = { 'location._unified.address.city': optimizedOptions.sort[sortField] };
      }
    }
    
    return { query: optimizedQuery, options: optimizedOptions };
  }

  /**
   * Create a lean query for better performance
   * @param {mongoose.Query} query - Mongoose query
   * @returns {mongoose.Query} Optimized query
   */
  static createLeanQuery(query) {
    // Use lean queries for better performance
    return query.lean({
      // Convert ObjectIds to strings for better serialization
      virtuals: true,
      // Don't include default values to reduce payload size
      defaults: false
    });
  }

  /**
   * Optimize document for storage
   * @param {Object} document - Document to optimize
   * @returns {Object} Optimized document
   */
  static optimizeForStorage(document) {
    const optimized = { ...document };
    
    // Remove redundant data
    if (optimized._internal && optimized._internal.rawData) {
      // Keep only essential raw data fields
      const essentialFields = ['url', 'source', 'externalId'];
      const minimalRawData = {};
      
      for (const field of essentialFields) {
        if (optimized._internal.rawData.original && 
            optimized._internal.rawData.original[field] !== undefined) {
          minimalRawData[field] = optimized._internal.rawData.original[field];
        }
      }
      
      // Replace full raw data with minimal version
      optimized._internal.rawData = {
        minimal: minimalRawData,
        preserved: true,
        timestamp: optimized._internal.rawData.timestamp || new Date()
      };
    }
    
    return optimized;
  }

  /**
   * Get database statistics for unified schema data
   * @param {mongoose.Model} model - Mongoose model
   * @returns {Promise<Object>} Database statistics
   */
  static async getDatabaseStats(model) {
    try {
      const stats = await model.collection.stats();
      const indexStats = await model.collection.indexInformation({ full: true });
      
      // Get count by source
      const sourceCounts = await model.aggregate([
        { $group: { _id: '$source', count: { $sum: 1 } } }
      ]).exec();
      
      // Get average document size
      const avgDocSize = stats.avgObjSize || 0;
      
      return {
        totalDocuments: stats.count,
        storageSize: stats.storageSize,
        indexSize: stats.totalIndexSize,
        avgDocumentSize: avgDocSize,
        indexCount: Object.keys(indexStats).length,
        sourceCounts: sourceCounts.map(item => ({
          source: item._id,
          count: item.count
        }))
      };
    } catch (error) {
      loggers.error.error(`Failed to get database stats: ${error.message}`, {
        error: error.message,
        stack: error.stack
      });
      return {
        error: error.message
      };
    }
  }
}

module.exports = {
  DatabaseOptimizer
};