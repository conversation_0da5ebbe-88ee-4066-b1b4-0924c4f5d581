const User = require('../models/User');
const AutoApplicationSettings = require('../models/AutoApplicationSettings');
const ApplicationQueue = require('../models/ApplicationQueue');
const ApplicationResult = require('../models/ApplicationResult');
const Document = require('../models/Document');
const auditLogService = require('./auditLogService');
const encryptionService = require('./encryptionService');
const documentVaultService = require('./documentVaultService');
const { loggers } = require('./logger');

/**
 * GDPRComplianceService - Handles GDPR compliance features
 * 
 * This service provides:
 * - User consent management
 * - Data export (Right to Access)
 * - Data deletion (Right to Erasure)
 * - Data portability
 * - Privacy transparency features
 */
class GDPRComplianceService {
  constructor() {
    this.consentTypes = {
      DATA_PROCESSING: 'data_processing',
      AUTO_APPLICATION: 'auto_application',
      MARKETING: 'marketing',
      ANALYTICS: 'analytics',
      THIRD_PARTY_SHARING: 'third_party_sharing'
    };

    this.dataCategories = {
      PERSONAL_INFO: 'personal_info',
      AUTO_APPLICATION_DATA: 'auto_application_data',
      DOCUMENTS: 'documents',
      APPLICATION_HISTORY: 'application_history',
      SYSTEM_LOGS: 'system_logs',
      PREFERENCES: 'preferences'
    };
  }

  /**
   * Record user consent
   * @param {string} userId - User ID
   * @param {string} consentType - Type of consent
   * @param {boolean} granted - Whether consent was granted
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<Object>} Consent record
   */
  async recordConsent(userId, consentType, granted, metadata = {}) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Initialize consent object if it doesn't exist
      if (!user.gdprConsent) {
        user.gdprConsent = {
          consents: new Map(),
          consentHistory: []
        };
      }

      const consentRecord = {
        type: consentType,
        granted,
        timestamp: new Date(),
        ipAddress: metadata.ipAddress,
        userAgent: metadata.userAgent,
        version: metadata.privacyPolicyVersion || '1.0',
        method: metadata.method || 'web_form' // web_form, api, email, etc.
      };

      // Update current consent status
      user.gdprConsent.consents.set(consentType, {
        granted,
        timestamp: consentRecord.timestamp,
        version: consentRecord.version
      });

      // Add to consent history
      user.gdprConsent.consentHistory.push(consentRecord);

      // Update last consent date
      user.gdprConsent.lastUpdated = new Date();

      await user.save();

      // Log the consent action
      await auditLogService.logPrivacy(userId, 'consent_given', {
        consentType,
        granted,
        version: consentRecord.version,
        method: consentRecord.method
      }, {
        ipAddress: metadata.ipAddress,
        userAgent: metadata.userAgent
      });

      loggers.app.info(`Consent ${granted ? 'granted' : 'withdrawn'} for user ${userId}, type: ${consentType}`);

      return {
        id: consentRecord.timestamp.getTime().toString(),
        type: consentType,
        granted,
        timestamp: consentRecord.timestamp,
        version: consentRecord.version
      };

    } catch (error) {
      loggers.app.error('Failed to record consent:', error);
      throw error;
    }
  }

  /**
   * Get user consent status
   * @param {string} userId - User ID
   * @param {string} consentType - Optional specific consent type
   * @returns {Promise<Object>} Consent status
   */
  async getConsentStatus(userId, consentType = null) {
    try {
      const user = await User.findById(userId);
      if (!user || !user.gdprConsent) {
        return { hasConsent: false, consents: {} };
      }

      if (consentType) {
        const consent = user.gdprConsent.consents.get(consentType);
        return {
          hasConsent: consent?.granted || false,
          consent: consent || null
        };
      }

      // Return all consents
      const consents = {};
      for (const [type, consent] of user.gdprConsent.consents) {
        consents[type] = consent;
      }

      return {
        hasConsent: Object.values(consents).some(c => c.granted),
        consents,
        lastUpdated: user.gdprConsent.lastUpdated,
        consentHistory: user.gdprConsent.consentHistory.slice(-10) // Last 10 consent changes
      };

    } catch (error) {
      loggers.app.error('Failed to get consent status:', error);
      throw error;
    }
  }

  /**
   * Export all user data (Right to Access)
   * @param {string} userId - User ID
   * @param {Object} options - Export options
   * @returns {Promise<Object>} Complete user data export
   */
  async exportUserData(userId, options = {}) {
    try {
      const { format = 'json', includeDocuments = false } = options;

      // Log the data export request
      await auditLogService.logPrivacy(userId, 'data_export_requested', {
        format,
        includeDocuments,
        requestedAt: new Date()
      });

      const exportData = {
        exportInfo: {
          userId,
          exportDate: new Date(),
          format,
          version: '1.0',
          dataCategories: []
        },
        userData: {},
        autoApplicationData: {},
        applicationHistory: {},
        documents: {},
        systemLogs: {},
        consentHistory: {}
      };

      // 1. Export basic user data
      const user = await User.findById(userId).lean();
      if (user) {
        // Decrypt sensitive fields for export
        const decryptedProfile = user.profile ? 
          encryptionService.decryptObject(user.profile, ['firstName', 'lastName', 'phoneNumber'], userId) : {};
        
        exportData.userData = {
          id: user._id,
          email: user.email,
          role: user.role,
          emailVerified: user.emailVerified,
          profile: decryptedProfile,
          preferences: user.preferences,
          notifications: user.notifications,
          createdAt: user.createdAt,
          lastActive: user.lastActive
        };
        exportData.exportInfo.dataCategories.push(this.dataCategories.PERSONAL_INFO);
      }

      // 2. Export auto-application settings
      const autoAppSettings = await AutoApplicationSettings.findOne({ userId }).lean();
      if (autoAppSettings) {
        // Decrypt personal info
        const decryptedPersonalInfo = encryptionService.decryptPersonalInfo(
          autoAppSettings.personalInfo, 
          userId
        );

        exportData.autoApplicationData = {
          enabled: autoAppSettings.enabled,
          settings: autoAppSettings.settings,
          criteria: autoAppSettings.criteria,
          personalInfo: decryptedPersonalInfo,
          statistics: autoAppSettings.statistics,
          createdAt: autoAppSettings.createdAt,
          updatedAt: autoAppSettings.updatedAt
        };
        exportData.exportInfo.dataCategories.push(this.dataCategories.AUTO_APPLICATION_DATA);
      }

      // 3. Export application history
      const [queueItems, applicationResults] = await Promise.all([
        ApplicationQueue.find({ userId }).lean(),
        ApplicationResult.find({ userId }).lean()
      ]);

      exportData.applicationHistory = {
        queuedApplications: queueItems.map(item => ({
          id: item._id,
          listingId: item.listingId,
          listingUrl: item.listingUrl,
          status: item.status,
          priority: item.priority,
          attempts: item.attempts,
          scheduledAt: item.scheduledAt,
          processedAt: item.processedAt,
          createdAt: item.createdAt
        })),
        applicationResults: applicationResults.map(result => ({
          id: result._id,
          listingId: result.listingId,
          status: result.status,
          submittedAt: result.submittedAt,
          confirmationNumber: result.confirmationNumber,
          response: result.response,
          metrics: result.metrics,
          createdAt: result.createdAt
        }))
      };
      
      if (queueItems.length > 0 || applicationResults.length > 0) {
        exportData.exportInfo.dataCategories.push(this.dataCategories.APPLICATION_HISTORY);
      }

      // 4. Export documents metadata (not actual files unless requested)
      const documents = await Document.find({ userId }).lean();
      exportData.documents = {
        documentList: documents.map(doc => ({
          id: doc._id,
          filename: doc.originalName,
          type: doc.type,
          size: doc.size,
          verified: doc.verified,
          verifiedAt: doc.verifiedAt,
          createdAt: doc.createdAt,
          expiryDate: doc.expiryDate,
          metadata: {
            securityLevel: doc.metadata?.securityLevel,
            containsPII: doc.metadata?.containsPII
          }
        })),
        totalDocuments: documents.length,
        totalSize: documents.reduce((sum, doc) => sum + doc.size, 0)
      };

      if (includeDocuments && documents.length > 0) {
        // Note: In a real implementation, you'd need to handle file exports
        exportData.documents.note = 'Document files can be downloaded separately using the document download API';
        exportData.exportInfo.dataCategories.push(this.dataCategories.DOCUMENTS);
      }

      // 5. Export recent audit logs
      const auditLogs = await auditLogService.getUserAuditLogs(userId, {
        limit: 1000,
        startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) // Last 90 days
      });

      exportData.systemLogs = {
        recentLogs: auditLogs,
        totalLogs: auditLogs.length,
        periodCovered: '90 days'
      };
      
      if (auditLogs.length > 0) {
        exportData.exportInfo.dataCategories.push(this.dataCategories.SYSTEM_LOGS);
      }

      // 6. Export consent history
      if (user?.gdprConsent) {
        exportData.consentHistory = {
          currentConsents: Object.fromEntries(user.gdprConsent.consents || new Map()),
          consentHistory: user.gdprConsent.consentHistory || [],
          lastUpdated: user.gdprConsent.lastUpdated
        };
      }

      // Log successful export
      await auditLogService.logPrivacy(userId, 'data_export_completed', {
        categoriesExported: exportData.exportInfo.dataCategories,
        totalSize: JSON.stringify(exportData).length
      });

      loggers.app.info(`Data export completed for user ${userId}`);

      return exportData;

    } catch (error) {
      loggers.app.error('Failed to export user data:', error);
      
      // Log failed export
      await auditLogService.logPrivacy(userId, 'data_export_failed', {
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Delete all user data (Right to Erasure)
   * @param {string} userId - User ID
   * @param {Object} options - Deletion options
   * @returns {Promise<Object>} Deletion summary
   */
  async deleteUserData(userId, options = {}) {
    try {
      const { 
        keepAuditLogs = true, 
        anonymizeInsteadOfDelete = false,
        reason = 'user_request'
      } = options;

      // Log the deletion request
      await auditLogService.logPrivacy(userId, 'data_deletion_requested', {
        reason,
        keepAuditLogs,
        anonymizeInsteadOfDelete,
        requestedAt: new Date()
      });

      const deletionSummary = {
        userId,
        deletionDate: new Date(),
        reason,
        deletedCategories: [],
        errors: []
      };

      try {
        // 1. Delete/anonymize auto-application settings
        const autoAppSettings = await AutoApplicationSettings.findOne({ userId });
        if (autoAppSettings) {
          if (anonymizeInsteadOfDelete) {
            // Anonymize personal info
            autoAppSettings.personalInfo = this._anonymizePersonalInfo(autoAppSettings.personalInfo);
            autoAppSettings.enabled = false;
            await autoAppSettings.save();
          } else {
            await AutoApplicationSettings.deleteOne({ userId });
          }
          deletionSummary.deletedCategories.push(this.dataCategories.AUTO_APPLICATION_DATA);
        }

        // 2. Delete application queue items
        const queueDeleteResult = await ApplicationQueue.deleteMany({ userId });
        if (queueDeleteResult.deletedCount > 0) {
          deletionSummary.deletedCategories.push('application_queue');
        }

        // 3. Delete/anonymize application results
        if (anonymizeInsteadOfDelete) {
          await ApplicationResult.updateMany(
            { userId },
            { 
              $unset: { 
                'formData.personalInfo': 1,
                'generatedContent': 1
              }
            }
          );
        } else {
          const resultsDeleteResult = await ApplicationResult.deleteMany({ userId });
          if (resultsDeleteResult.deletedCount > 0) {
            deletionSummary.deletedCategories.push(this.dataCategories.APPLICATION_HISTORY);
          }
        }

        // 4. Delete documents
        const documents = await Document.find({ userId });
        for (const doc of documents) {
          try {
            await documentVaultService.deleteDocument(doc._id, userId, 'user', 'system');
          } catch (error) {
            deletionSummary.errors.push(`Failed to delete document ${doc._id}: ${error.message}`);
          }
        }
        if (documents.length > 0) {
          deletionSummary.deletedCategories.push(this.dataCategories.DOCUMENTS);
        }

        // 5. Delete/anonymize user profile
        const user = await User.findById(userId);
        if (user) {
          if (anonymizeInsteadOfDelete) {
            // Anonymize user data
            user.email = `deleted-user-${userId}@anonymized.local`;
            user.profile = this._anonymizeUserProfile(user.profile);
            user.preferences = {};
            user.autoApplication = undefined;
            user.documents = [];
            user.gdprConsent = {
              consents: new Map(),
              consentHistory: [],
              lastUpdated: new Date()
            };
            await user.save();
          } else {
            await User.deleteOne({ _id: userId });
          }
          deletionSummary.deletedCategories.push(this.dataCategories.PERSONAL_INFO);
        }

        // 6. Handle audit logs
        if (!keepAuditLogs) {
          // Delete audit logs (not recommended for compliance)
          const auditLogModel = require('mongoose').model('AuditLog');
          await auditLogModel.deleteMany({ userId });
          deletionSummary.deletedCategories.push(this.dataCategories.SYSTEM_LOGS);
        } else {
          // Anonymize audit logs
          const auditLogModel = require('mongoose').model('AuditLog');
          await auditLogModel.updateMany(
            { userId },
            { 
              $set: { 
                'metadata.ipAddress': 'anonymized',
                'metadata.userAgent': 'anonymized',
                'details.personalInfo': 'anonymized'
              }
            }
          );
        }

        // Log successful deletion
        await auditLogService.logPrivacy(userId, 'data_deletion_completed', {
          categoriesDeleted: deletionSummary.deletedCategories,
          anonymized: anonymizeInsteadOfDelete,
          errors: deletionSummary.errors
        });

        loggers.app.info(`Data deletion completed for user ${userId}`, {
          categories: deletionSummary.deletedCategories,
          errors: deletionSummary.errors.length
        });

        return deletionSummary;

      } catch (error) {
        deletionSummary.errors.push(`General deletion error: ${error.message}`);
        throw error;
      }

    } catch (error) {
      loggers.app.error('Failed to delete user data:', error);
      
      // Log failed deletion
      await auditLogService.logPrivacy(userId, 'data_deletion_failed', {
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Get privacy transparency report
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Privacy transparency report
   */
  async getPrivacyTransparencyReport(userId) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const report = {
        userId,
        generatedAt: new Date(),
        dataProcessing: {
          purposes: [
            {
              purpose: 'Auto-application to rental properties',
              legalBasis: 'Consent (GDPR Art. 6(1)(a))',
              dataTypes: ['Personal information', 'Employment details', 'Financial information'],
              retention: '3 years after account deletion',
              automated: true
            },
            {
              purpose: 'User account management',
              legalBasis: 'Contract performance (GDPR Art. 6(1)(b))',
              dataTypes: ['Email', 'Profile information', 'Preferences'],
              retention: 'Duration of account + 1 year',
              automated: false
            },
            {
              purpose: 'Security and fraud prevention',
              legalBasis: 'Legitimate interest (GDPR Art. 6(1)(f))',
              dataTypes: ['IP address', 'Login logs', 'Device information'],
              retention: '2 years',
              automated: true
            }
          ]
        },
        dataSharing: {
          thirdParties: [
            {
              name: 'Funda.nl',
              purpose: 'Property application submission',
              dataTypes: ['Personal information', 'Application letters'],
              country: 'Netherlands',
              safeguards: 'Standard contractual clauses'
            }
          ]
        },
        userRights: {
          access: {
            available: true,
            description: 'You can export all your data at any time',
            lastExercised: null // Would be populated from audit logs
          },
          rectification: {
            available: true,
            description: 'You can update your personal information in your profile'
          },
          erasure: {
            available: true,
            description: 'You can request deletion of all your data',
            conditions: 'Account will be permanently deleted'
          },
          portability: {
            available: true,
            description: 'You can export your data in JSON format'
          },
          objection: {
            available: true,
            description: 'You can object to processing for direct marketing'
          },
          restrictProcessing: {
            available: true,
            description: 'You can request to limit how we process your data'
          }
        },
        consentStatus: await this.getConsentStatus(userId),
        dataRetention: {
          personalData: '3 years after account deletion',
          applicationHistory: '3 years after submission',
          auditLogs: '7 years (legal requirement)',
          documents: 'Until manually deleted or account deletion'
        },
        contact: {
          dataProtectionOfficer: '<EMAIL>',
          supervisoryAuthority: 'Autoriteit Persoonsgegevens (AP)',
          complaintProcess: 'Contact DPO first, then AP if unresolved'
        }
      };

      return report;

    } catch (error) {
      loggers.app.error('Failed to generate privacy transparency report:', error);
      throw error;
    }
  }

  /**
   * Check if user has required consent for auto-application
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Whether user has required consent
   */
  async hasAutoApplicationConsent(userId) {
    try {
      const consentStatus = await this.getConsentStatus(userId, this.consentTypes.AUTO_APPLICATION);
      return consentStatus.hasConsent;
    } catch (error) {
      loggers.app.error('Failed to check auto-application consent:', error);
      return false;
    }
  }

  /**
   * Validate consent before processing
   * @param {string} userId - User ID
   * @param {string} operation - Operation requiring consent
   * @returns {Promise<boolean>} Whether operation is allowed
   */
  async validateConsentForOperation(userId, operation) {
    try {
      const requiredConsents = this._getRequiredConsents(operation);
      
      for (const consentType of requiredConsents) {
        const consentStatus = await this.getConsentStatus(userId, consentType);
        if (!consentStatus.hasConsent) {
          loggers.app.warn(`Missing consent for operation ${operation}`, {
            userId,
            missingConsent: consentType
          });
          return false;
        }
      }

      return true;

    } catch (error) {
      loggers.app.error('Failed to validate consent:', error);
      return false;
    }
  }

  // Private helper methods

  /**
   * Get required consents for operation
   * @private
   */
  _getRequiredConsents(operation) {
    const consentMap = {
      'auto_application': [this.consentTypes.DATA_PROCESSING, this.consentTypes.AUTO_APPLICATION],
      'document_upload': [this.consentTypes.DATA_PROCESSING],
      'profile_update': [this.consentTypes.DATA_PROCESSING],
      'marketing_email': [this.consentTypes.MARKETING],
      'analytics': [this.consentTypes.ANALYTICS]
    };

    return consentMap[operation] || [this.consentTypes.DATA_PROCESSING];
  }

  /**
   * Anonymize personal information
   * @private
   */
  _anonymizePersonalInfo(personalInfo) {
    if (!personalInfo) return personalInfo;

    return {
      fullName: 'ANONYMIZED',
      email: '<EMAIL>',
      phone: 'ANONYMIZED',
      dateOfBirth: null,
      nationality: 'ANONYMIZED',
      occupation: 'ANONYMIZED',
      employer: 'ANONYMIZED',
      monthlyIncome: 0,
      moveInDate: null,
      leaseDuration: 0,
      numberOfOccupants: 1,
      hasGuarantor: false,
      guarantorInfo: {},
      emergencyContact: {}
    };
  }

  /**
   * Anonymize user profile
   * @private
   */
  _anonymizeUserProfile(profile) {
    if (!profile) return profile;

    return {
      firstName: 'ANONYMIZED',
      lastName: 'USER',
      phoneNumber: 'ANONYMIZED',
      dateOfBirth: null,
      nationality: 'ANONYMIZED',
      userType: [],
      employment: {
        occupation: 'ANONYMIZED',
        employer: 'ANONYMIZED',
        monthlyIncome: 0
      },
      socialPreferences: {
        lookingForRoommate: false,
        isVisible: false
      }
    };
  }
}

module.exports = new GDPRComplianceService();