const ErrorHandlingService = require('../services/errorHandlingService');
const { loggers } = require('../services/logger');

/**
 * Error Handling Middleware for Express routes
 * Integrates with the comprehensive error handling service
 */
class ErrorHandlingMiddleware {
  constructor() {
    this.errorHandlingService = new ErrorHandlingService();
    this.logger = loggers.api || loggers.app;
  }

  /**
   * Express error handling middleware
   * @param {Error} error - The error that occurred
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  handleError = async (error, req, res, next) => {
    try {
      // Extract context from request
      const context = {
        service: 'API',
        method: req.method,
        url: req.originalUrl,
        userId: req.user?.id || req.body?.userId || req.params?.userId,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        operation: this.extractOperationFromUrl(req.originalUrl),
        requestId: req.id || req.headers['x-request-id']
      };

      // Handle the error using the comprehensive error handling service
      const recoveryResult = await this.errorHandlingService.handleError(error, context);

      // Log the error with context
      this.logger.error('API Error:', {
        error: error.message,
        stack: error.stack,
        context,
        recoveryResult
      });

      // Determine appropriate HTTP response
      const response = this.buildErrorResponse(error, context, recoveryResult);

      // Send response
      res.status(response.statusCode).json(response.body);

    } catch (handlingError) {
      // Fallback error handling if the error handling service fails
      this.logger.error('Error in error handling middleware:', handlingError);
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred. Please try again later.',
        requestId: req.id || req.headers['x-request-id']
      });
    }
  };

  /**
   * Async route wrapper to catch errors and pass to error handler
   * @param {Function} fn - Async route handler function
   * @returns {Function} Wrapped route handler
   */
  asyncHandler = (fn) => {
    return (req, res, next) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  };

  /**
   * Validation error handler for request validation failures
   * @param {Object} validationErrors - Validation error details
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  handleValidationError = async (validationErrors, req, res) => {
    const error = new Error('Validation failed');
    error.validationErrors = validationErrors;

    const context = {
      service: 'API',
      method: req.method,
      url: req.originalUrl,
      userId: req.user?.id,
      operation: 'validation',
      validationErrors
    };

    const recoveryResult = await this.errorHandlingService.handleError(error, context);

    res.status(400).json({
      success: false,
      error: 'Validation Error',
      message: 'The request data is invalid',
      details: validationErrors,
      requestId: req.id || req.headers['x-request-id']
    });
  };

  /**
   * Rate limiting error handler
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Object} rateLimitInfo - Rate limit information
   */
  handleRateLimitError = async (req, res, rateLimitInfo) => {
    const error = new Error('Rate limit exceeded');
    
    const context = {
      service: 'API',
      method: req.method,
      url: req.originalUrl,
      userId: req.user?.id,
      operation: 'rate_limit',
      rateLimitInfo
    };

    const recoveryResult = await this.errorHandlingService.handleError(error, context);

    res.status(429).json({
      success: false,
      error: 'Rate Limit Exceeded',
      message: 'Too many requests. Please try again later.',
      retryAfter: rateLimitInfo.retryAfter || 60,
      requestId: req.id || req.headers['x-request-id']
    });
  };

  /**
   * Authentication error handler
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {string} reason - Authentication failure reason
   */
  handleAuthenticationError = async (req, res, reason = 'Authentication required') => {
    const error = new Error(reason);
    
    const context = {
      service: 'API',
      method: req.method,
      url: req.originalUrl,
      operation: 'authentication',
      reason
    };

    const recoveryResult = await this.errorHandlingService.handleError(error, context);

    res.status(401).json({
      success: false,
      error: 'Authentication Error',
      message: reason,
      requestId: req.id || req.headers['x-request-id']
    });
  };

  /**
   * Authorization error handler
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {string} reason - Authorization failure reason
   */
  handleAuthorizationError = async (req, res, reason = 'Insufficient permissions') => {
    const error = new Error(reason);
    
    const context = {
      service: 'API',
      method: req.method,
      url: req.originalUrl,
      userId: req.user?.id,
      operation: 'authorization',
      reason
    };

    const recoveryResult = await this.errorHandlingService.handleError(error, context);

    res.status(403).json({
      success: false,
      error: 'Authorization Error',
      message: reason,
      requestId: req.id || req.headers['x-request-id']
    });
  };

  /**
   * Extract operation name from URL for better error categorization
   * @param {string} url - Request URL
   * @returns {string} Operation name
   */
  extractOperationFromUrl(url) {
    const urlParts = url.split('/').filter(part => part.length > 0);
    
    if (urlParts.includes('auto-application')) {
      if (urlParts.includes('enable')) return 'enable_auto_application';
      if (urlParts.includes('disable')) return 'disable_auto_application';
      if (urlParts.includes('settings')) return 'manage_settings';
      if (urlParts.includes('history')) return 'get_history';
      if (urlParts.includes('documents')) return 'manage_documents';
      return 'auto_application';
    }
    
    if (urlParts.includes('queue')) return 'queue_management';
    if (urlParts.includes('applications')) return 'application_management';
    if (urlParts.includes('users')) return 'user_management';
    
    return 'unknown';
  }

  /**
   * Build appropriate error response based on error type and recovery result
   * @param {Error} error - The original error
   * @param {Object} context - Error context
   * @param {Object} recoveryResult - Result from error handling service
   * @returns {Object} Response object with statusCode and body
   */
  buildErrorResponse(error, context, recoveryResult) {
    // Determine status code based on error type
    let statusCode = 500; // Default to internal server error
    let errorType = 'Internal Server Error';
    let message = 'An unexpected error occurred';
    let userFriendlyMessage = null;

    // Categorize error for appropriate HTTP status
    if (error.message.includes('not found') || error.message.includes('Not found')) {
      statusCode = 404;
      errorType = 'Not Found';
      message = 'The requested resource was not found';
    } else if (error.message.includes('validation') || error.message.includes('invalid')) {
      statusCode = 400;
      errorType = 'Bad Request';
      message = 'The request data is invalid';
    } else if (error.message.includes('unauthorized') || error.message.includes('authentication')) {
      statusCode = 401;
      errorType = 'Unauthorized';
      message = 'Authentication is required';
    } else if (error.message.includes('forbidden') || error.message.includes('permission')) {
      statusCode = 403;
      errorType = 'Forbidden';
      message = 'You do not have permission to perform this action';
    } else if (error.message.includes('rate limit') || error.message.includes('too many')) {
      statusCode = 429;
      errorType = 'Too Many Requests';
      message = 'Rate limit exceeded. Please try again later';
    } else if (error.message.includes('timeout') || error.message.includes('connection')) {
      statusCode = 503;
      errorType = 'Service Unavailable';
      message = 'The service is temporarily unavailable';
    }

    // Add user-friendly message if recovery result provides one
    if (recoveryResult && recoveryResult.userFriendlyMessage) {
      userFriendlyMessage = recoveryResult.userFriendlyMessage;
    }

    const response = {
      statusCode,
      body: {
        success: false,
        error: errorType,
        message,
        requestId: context.requestId
      }
    };

    // Add additional information based on recovery result
    if (recoveryResult) {
      if (recoveryResult.retryScheduled) {
        response.body.retryInfo = {
          retryScheduled: true,
          retryDelay: recoveryResult.retryDelay,
          retryAttempt: recoveryResult.retryAttempt
        };
      }

      if (recoveryResult.manualInterventionRequired) {
        response.body.manualInterventionRequired = true;
        response.body.interventionMessage = userFriendlyMessage || 
          'Manual intervention is required. Please contact support if the issue persists.';
      }

      if (recoveryResult.degradationMode) {
        response.body.systemStatus = 'degraded';
        response.body.message = 'The system is operating in degraded mode. Some features may be limited.';
      }
    }

    // In development, include stack trace
    if (process.env.NODE_ENV === 'development') {
      response.body.stack = error.stack;
      response.body.context = context;
      response.body.recoveryResult = recoveryResult;
    }

    return response;
  }

  /**
   * Health check endpoint for error handling service
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  healthCheck = async (req, res) => {
    try {
      const stats = this.errorHandlingService.getErrorStatistics();
      
      res.json({
        success: true,
        errorHandling: {
          status: 'operational',
          statistics: stats,
          categories: Object.keys(this.errorHandlingService.errorCategories),
          recoveryStrategies: Object.keys(this.errorHandlingService.recoveryStrategies)
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Health check failed',
        message: error.message
      });
    }
  };

  /**
   * Reset error statistics endpoint (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  resetStatistics = async (req, res) => {
    try {
      this.errorHandlingService.resetErrorStatistics();
      
      res.json({
        success: true,
        message: 'Error statistics have been reset'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to reset statistics',
        message: error.message
      });
    }
  };

  /**
   * Get error handling configuration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getConfiguration = async (req, res) => {
    try {
      const config = {
        errorCategories: this.errorHandlingService.errorCategories,
        recoveryStrategies: Object.keys(this.errorHandlingService.recoveryStrategies),
        healthMetrics: this.errorHandlingService.getErrorStatistics()
      };
      
      res.json({
        success: true,
        configuration: config
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to get configuration',
        message: error.message
      });
    }
  };
}

// Create singleton instance
const errorHandlingMiddleware = new ErrorHandlingMiddleware();

module.exports = {
  ErrorHandlingMiddleware,
  errorHandlingMiddleware,
  
  // Export commonly used methods
  handleError: errorHandlingMiddleware.handleError,
  asyncHandler: errorHandlingMiddleware.asyncHandler,
  handleValidationError: errorHandlingMiddleware.handleValidationError,
  handleRateLimitError: errorHandlingMiddleware.handleRateLimitError,
  handleAuthenticationError: errorHandlingMiddleware.handleAuthenticationError,
  handleAuthorizationError: errorHandlingMiddleware.handleAuthorizationError,
  healthCheck: errorHandlingMiddleware.healthCheck,
  resetStatistics: errorHandlingMiddleware.resetStatistics,
  getConfiguration: errorHandlingMiddleware.getConfiguration
};