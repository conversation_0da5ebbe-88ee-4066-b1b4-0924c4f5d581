/**
 * Transformation Integration Module
 * 
 * This module integrates the unified schema transformation pipeline with existing scrapers
 * by providing a compatible validateAndNormalizeListing function that maintains the same
 * interface while using the new transformation system under the hood.
 * 
 * Includes performance optimizations for the transformation pipeline:
 * - Caching for frequently used transformation rules and mappings
 * - Concurrent processing support for multiple scrapers
 * - Memory usage optimizations for large batches
 */

const { FieldMappingRegistry } = require('./fieldMappingRegistry');
const { MappingConfigLoader } = require('./mappingConfigLoader');
const { SchemaTransformer } = require('./schemaTransformer');
const { ValidationEngine } = require('./validationEngine');
const { OptimizedSchemaTransformer } = require('./transformationOptimizer');
const { SchemaError, ErrorTypes } = require('../utils/schemaErrors');
const { loggers } = require('./logger');
const { convertToFrontendFormat } = require('./frontendCompatibilityLayer');
const path = require('path');

// Initialize the transformation pipeline components
let fieldMappingRegistry = null;
let baseTransformer = null;
let optimizedTransformer = null;
let validationEngine = null;
let isInitialized = false;

/**
 * Initialize the transformation pipeline
 * @returns {Promise<void>}
 */
async function initializeTransformationPipeline() {
  if (isInitialized) return;
  
  try {
    // Create field mapping registry
    fieldMappingRegistry = new FieldMappingRegistry();
    
    // Create mapping config loader
    const mappingLoader = new MappingConfigLoader(fieldMappingRegistry);
    
    // Load mappings from configuration files
    const mappingsDir = path.join(__dirname, '../config/mappings');
    await mappingLoader.loadFromDirectory(mappingsDir);
    
    // Create base schema transformer
    baseTransformer = new SchemaTransformer(fieldMappingRegistry);
    
    // Create optimized schema transformer with caching and concurrent processing
    optimizedTransformer = new OptimizedSchemaTransformer(baseTransformer, {
      cache: {
        stdTTL: 600, // 10 minutes
        maxKeys: 5000 // Store up to 5000 cached transformations
      },
      concurrent: {
        maxWorkers: Math.max(1, require('os').cpus().length - 1), // Use all but one CPU core
        minBatchSize: 5 // Use concurrent processing for batches of 5 or more
      }
    });
    
    // Create validation engine
    validationEngine = new ValidationEngine();
    
    isInitialized = true;
    loggers.app.info('Optimized transformation pipeline initialized successfully');
  } catch (error) {
    loggers.error.error('Failed to initialize transformation pipeline', {
      error: error.message,
      stack: error.stack
    });
    
    // Fall back to default implementations if initialization fails
    isInitialized = false;
    throw new Error(`Transformation pipeline initialization failed: ${error.message}`);
  }
}

/**
 * Determine the source identifier from the listing data
 * @param {Object} listingData - Raw listing data
 * @returns {string} Source identifier
 */
function determineSource(listingData) {
  if (!listingData || !listingData.source) {
    return null;
  }
  
  const source = listingData.source.toLowerCase();
  
  if (source.includes('funda')) {
    return 'funda';
  } else if (source.includes('huurwoningen')) {
    return 'huurwoningen';
  } else if (source.includes('pararius')) {
    return 'pararius';
  }
  
  return null;
}

/**
 * Enhanced validateAndNormalizeListing function that uses the unified schema transformer
 * while maintaining backward compatibility with the existing function signature
 * 
 * @param {Object} listingData - Raw listing data from scraper
 * @returns {Object|null} Normalized listing data or null if invalid
 */
async function validateAndNormalizeListingEnhanced(listingData) {
  // Ensure the transformation pipeline is initialized
  if (!isInitialized) {
    try {
      await initializeTransformationPipeline();
    } catch (error) {
      // Log the error and fall back to the original implementation
      loggers.error.error('Failed to initialize transformation pipeline, falling back to original implementation', {
        error: error.message
      });
      
      // Import the original implementation
      const { validateAndNormalizeListing: originalImplementation } = require('./scraperUtils');
      return originalImplementation(listingData);
    }
  }
  
  // Skip processing if listing data is invalid
  if (!listingData || !listingData.title || !listingData.url || !listingData.location) {
    return null;
  }
  
  try {
    // Determine the source for mapping selection
    const source = determineSource(listingData);
    
    if (!source || !fieldMappingRegistry.hasSource(source)) {
      loggers.scraper.warn('No mapping configuration found for source', {
        source: listingData.source,
        title: listingData.title
      });
      
      // Fall back to the original implementation
      const { validateAndNormalizeListing: originalImplementation } = require('./scraperUtils');
      return originalImplementation(listingData);
    }
    
    // Transform the listing data using the optimized schema transformer
    const transformedData = await optimizedTransformer.transform(listingData, source, {
      validateOutput: true,
      preserveRawData: true,
      throwOnError: false,
      useCache: true // Enable caching for better performance
    });
    
    // Calculate and update data quality metrics
    baseTransformer.updateDataQuality(transformedData);
    
    // Convert the transformed data to a format compatible with the existing system
    const compatibleData = convertToCompatibleFormat(transformedData);
    
    // Log successful transformation
    loggers.scraper.debug('Listing transformed successfully', {
      source,
      title: compatibleData.title,
      quality: transformedData._internal?.dataQuality
    });
    
    return compatibleData;
  } catch (error) {
    // Log the error
    loggers.scraper.error('Error transforming listing', {
      error: error instanceof SchemaError ? error.toJSON() : {
        message: error.message,
        stack: error.stack
      },
      source: listingData.source,
      title: listingData.title
    });
    
    // Fall back to the original implementation
    const { validateAndNormalizeListing: originalImplementation } = require('./scraperUtils');
    return originalImplementation(listingData);
  }
}

/**
 * Convert transformed data to a format compatible with the existing system
 * @param {Object} transformedData - Transformed data from schema transformer
 * @returns {Object} Compatible data format
 */
function convertToCompatibleFormat(transformedData) {
  if (!transformedData) return null;
  
  // Use the frontend compatibility layer to convert the data
  const frontendData = convertToFrontendFormat(transformedData);
  
  // Ensure dateAdded is a Date object for database compatibility
  if (frontendData.dateAdded && !(frontendData.dateAdded instanceof Date)) {
    frontendData.dateAdded = new Date(frontendData.dateAdded || Date.now());
  }
  
  // Add unified schema data for future use
  frontendData.unifiedData = transformedData;
  
  return frontendData;
}

/**
 * Original implementation of validateAndNormalizeListing
 * This is a copy of the function from scraperUtils.js to avoid circular dependencies
 * 
 * @param {Object} listingData - Raw listing data from scraper
 * @returns {Object|null} Normalized listing data or null if invalid
 */
function validateAndNormalizeListingOriginal(listingData) {
  if (!listingData.title || !listingData.url || !listingData.location) {
    return null; // Invalid listing
  }

  // Normalize title
  let normalizedTitle = listingData.title.trim();
  // Remove extra whitespace and normalize
  normalizedTitle = normalizedTitle.replace(/\s+/g, " ");
  // Ensure proper capitalization
  if (normalizedTitle.length > 0) {
    normalizedTitle =
      normalizedTitle.charAt(0).toUpperCase() + normalizedTitle.slice(1);
  }

  // Normalize price
  let normalizedPrice = listingData.price;
  if (normalizedPrice && typeof normalizedPrice === "string") {
    // Clean up HTML entities, non-breaking spaces, and extra content
    normalizedPrice = normalizedPrice
      .replace(/&nbsp;/g, " ")
      .replace(/\u00A0/g, " ") // Non-breaking space character (160)
      .replace(/\s+/g, " ")
      .trim();

    // Extract only the price part (before any newlines or extra content)
    const priceLineMatch = normalizedPrice.match(/^([^\\n]+)/);
    if (priceLineMatch) {
      normalizedPrice = priceLineMatch[1].trim();
    }

    // Handle common Dutch price formats
    if (
      normalizedPrice.toLowerCase().includes("op aanvraag") ||
      normalizedPrice.toLowerCase().includes("on request")
    ) {
      normalizedPrice = "Prijs op aanvraag";
    } else {
      // Extract numeric value and format consistently
      const priceMatch = normalizedPrice.match(/€\s*([\d.,]+)/); // Modified to capture numbers with thousands separators
      if (priceMatch) {
        // Handle European number formats where comma can be thousands separator
        let extractedPrice = priceMatch[1].trim();

        let numericPrice;

        // Special case for the format in the screenshot: €3,950 (treat as 3950, not 3.95)
        if (extractedPrice.match(/^\d{1,3},\d{3}$/) && !normalizedPrice.includes('.')) {
          // This is the format from the screenshot - comma is a thousands separator
          numericPrice = parseFloat(extractedPrice.replace(/,/g, ''));
        }
        // Case 1: Format with multiple thousands separators and decimal comma (e.g., 1.234.567,89)
        else if (extractedPrice.match(/\d{1,3}(\.\d{3})+,\d+$/)) {
          numericPrice = parseFloat(
            extractedPrice.replace(/\./g, '').replace(',', '.')
          );
        }
        // Case 2: Format with single thousands separator and decimal comma (e.g., 3.950,00)
        else if (extractedPrice.match(/\d{1,3}\.\d{3},\d+$/)) {
          numericPrice = parseFloat(
            extractedPrice.replace(/\./g, '').replace(',', '.')
          );
        }
        // Case 3: Format with comma as thousands separator (e.g., 3,950)
        else if (extractedPrice.match(/\d{1,3},\d{3}$/)) {
          numericPrice = parseFloat(extractedPrice.replace(/,/g, ''));
        }
        // Case 4: Format with period as thousands separator (e.g., 3.950)
        else if (extractedPrice.match(/\d{1,3}\.\d{3}$/)) {
          numericPrice = parseFloat(extractedPrice.replace(/\./g, ''));
        }
        // Case 5: Regular decimal format or other formats
        else {
          numericPrice = parseFloat(extractedPrice.replace(',', '.'));
        }
        if (numericPrice > 0) {
          normalizedPrice = `€ ${Math.round(numericPrice).toLocaleString(
            "nl-NL"
          )}`;
          // Add per month if it seems to be a rental price
          if (numericPrice < 10000) {
            normalizedPrice += " per maand";
          }
        }
      }
    }
  } else {
    normalizedPrice = "Prijs op aanvraag";
  }

  // Normalize location
  let normalizedLocation = listingData.location.replace(/\s+/g, " ").trim();

  // Remove common prefixes and clean up
  normalizedLocation = normalizedLocation
    .replace(/^(huis|appartement|kamer|woning)\s+/i, "")
    .toLowerCase()
    .replace(/\b\w/g, (l) => l.toUpperCase()); // Title case

  // Validate URL
  const isValidUrl =
    listingData.url.startsWith("http") &&
    (listingData.url.includes("funda.nl") ||
      listingData.url.includes("pararius.nl") ||
      listingData.url.includes("huurwoningen.nl"));

  if (!isValidUrl) {
    return null;
  }

  // Validate property type
  const validPropertyTypes = [
    "huis",
    "appartement",
    "kamer",
    "parkeergelegenheid",
    "woning",
    "studio"
  ];
  let normalizedPropertyType = listingData.propertyType || "woning";
  if (!validPropertyTypes.includes(normalizedPropertyType)) {
    normalizedPropertyType = "woning";
  }

  return {
    ...listingData,
    title: normalizedTitle,
    price: normalizedPrice,
    location: normalizedLocation,
    propertyType: normalizedPropertyType,
    dateAdded: new Date(),
  };
}

/**
 * Synchronous wrapper for validateAndNormalizeListingEnhanced that maintains
 * the same interface as the original function
 * 
 * @param {Object} listingData - Raw listing data from scraper
 * @returns {Object|null} Normalized listing data or null if invalid
 */
function validateAndNormalizeListing(listingData) {
  // For now, just use the original implementation
  // In the future, we can make this use the enhanced version
  // once we've resolved the circular dependency issues
  return validateAndNormalizeListingOriginal(listingData);
}

module.exports = {
  validateAndNormalizeListing,
  validateAndNormalizeListingEnhanced,
  initializeTransformationPipeline
};